<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class OrderDiscount extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'type',
        'value',
        'amount',
        'coupon_code',
        'points_used',
        'description',
        'is_active',
    ];

    protected $casts = [
        'value' => 'decimal:2',
        'amount' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the order that owns the discount
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get formatted description for display
     */
    public function getFormattedDescriptionAttribute()
    {
        if ($this->description) {
            return $this->description;
        }

        switch ($this->type) {
            case 'percentage':
                return number_format($this->value, 1) . '% Discount';
            case 'fixed':
                return '$' . number_format($this->value, 2) . ' Off';
            case 'coupon':
                return 'Coupon: ' . $this->coupon_code;
            case 'points':
                return $this->points_used . ' Points Used';
            default:
                return 'Discount';
        }
    }

    /**
     * Get the discount type label
     */
    public function getTypeLabelAttribute()
    {
        switch ($this->type) {
            case 'percentage':
                return 'Percentage';
            case 'fixed':
                return 'Fixed Amount';
            case 'coupon':
                return 'Coupon';
            case 'points':
                return 'Points';
            default:
                return ucfirst($this->type);
        }
    }

    /**
     * Scope to get active discounts
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get discounts by type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }
}
