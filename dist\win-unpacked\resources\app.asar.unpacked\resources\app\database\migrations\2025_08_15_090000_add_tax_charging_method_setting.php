<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add new tax charging method setting only if it doesn't exist
        $exists = DB::table('order_settings')->where('key', 'tax_charging_method')->exists();
        
        if (!$exists) {
            DB::table('order_settings')->insert([
                [
                    'key' => 'tax_charging_method',
                    'value' => 'customer_pays',
                    'type' => 'select',
                    'category' => 'tax',
                    'label' => 'Tax Charging Method',
                    'description' => 'Choose whether customers pay tax separately or business absorbs tax in prices',
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('order_settings')->where('key', 'tax_charging_method')->delete();
    }
};