<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Supplier extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'contact_info',
    ];

    /**
     * Get the purchases from this supplier.
     */
    public function purchases()
    {
        return $this->hasMany(Purchase::class);
    }
}
