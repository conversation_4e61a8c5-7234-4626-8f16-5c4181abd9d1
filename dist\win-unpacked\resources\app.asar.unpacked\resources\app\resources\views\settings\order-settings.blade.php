<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Order Settings') }}
            </h2>
            <div class="flex space-x-2">
                <form action="{{ route('settings.order-settings.reset') }}" method="POST" class="inline-block"
                      onsubmit="return confirm('Are you sure you want to reset all settings to default values?')">
                    @csrf
                    <button type="submit" class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded">
                        Reset to Defaults
                    </button>
                </form>
                <a href="{{ route('pos.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to POS
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    {{ session('error') }}
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Configure Order Settings</h3>
                        <p class="text-gray-600">Manage tax rates, service charges, and other order-related configurations.</p>
                    </div>

                    <form action="{{ route('settings.order-settings.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Settings by Category -->
                        @foreach($settingsByCategory as $category => $settings)
                            <div class="mb-8">
                                <div class="bg-gray-50 px-4 py-3 border-l-4 border-blue-500 mb-4">
                                    <h4 class="text-lg font-semibold text-gray-800 capitalize">
                                        {{ str_replace('_', ' ', $category) }} Settings
                                    </h4>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    @foreach($settings as $setting)
                                        <div class="space-y-2">
                                            <label for="setting_{{ $setting->key }}" class="block text-sm font-medium text-gray-700">
                                                {{ $setting->label }}
                                            </label>
                                            
                                            @if($setting->type === 'boolean')
                                                <div class="flex items-center">
                                                    <input type="checkbox"
                                                           id="setting_{{ $setting->key }}"
                                                           name="settings[{{ $setting->key }}]"
                                                           value="true"
                                                           {{ $setting->value === 'true' ? 'checked' : '' }}
                                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                    <label for="setting_{{ $setting->key }}" class="ml-2 text-sm text-gray-600">
                                                        Enable {{ $setting->label }}
                                                    </label>
                                                </div>
                                            @elseif($setting->type === 'number')
                                                <div class="relative">
                                                    <input type="number"
                                                           id="setting_{{ $setting->key }}"
                                                           name="settings[{{ $setting->key }}]"
                                                           value="{{ $setting->value }}"
                                                           step="0.01"
                                                           min="0"
                                                           class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                                    @if(str_contains($setting->key, 'rate') || str_contains($setting->key, 'percentage'))
                                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                                            <span class="text-gray-500 sm:text-sm">
                                                                {{ str_contains($setting->key, 'rate') ? '(decimal)' : '%' }}
                                                            </span>
                                                        </div>
                                                    @endif
                                                </div>
                                            @elseif($setting->type === 'select' && $setting->key === 'tax_charging_method')
                                                <select id="setting_{{ $setting->key }}"
                                                        name="settings[{{ $setting->key }}]"
                                                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                                    <option value="customer_pays" {{ $setting->value === 'customer_pays' ? 'selected' : '' }}>
                                                        💰 Customer Pays Tax (tax added to final price)
                                                    </option>
                                                    <option value="business_absorbs" {{ $setting->value === 'business_absorbs' ? 'selected' : '' }}>
                                                        🏢 Business Absorbs Tax (tax included in price)
                                                    </option>
                                                </select>
                                            @else
                                                <input type="text"
                                                       id="setting_{{ $setting->key }}"
                                                       name="settings[{{ $setting->key }}]"
                                                       value="{{ $setting->value }}"
                                                       class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                            @endif

                                            @if($setting->description)
                                                <p class="text-xs text-gray-500">{{ $setting->description }}</p>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach

                        <!-- Save Button -->
                        <div class="flex justify-end space-x-4 pt-6 border-t">
                            <button type="button" onclick="previewSettings()" 
                                    class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
                                Preview Changes
                            </button>
                            <button type="submit" 
                                    class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded">
                                Save Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Settings Preview Modal -->
            <div id="previewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
                <div class="flex items-center justify-center min-h-screen p-4">
                    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-96 overflow-y-auto">
                        <div class="p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold">Settings Preview</h3>
                                <button onclick="closePreview()" class="text-gray-400 hover:text-gray-600">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                            <div id="previewContent" class="space-y-4">
                                <!-- Preview content will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function previewSettings() {
            const form = document.querySelector('form');
            const formData = new FormData(form);
            const previewContent = document.getElementById('previewContent');
            
            let previewHtml = '<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">';
            previewHtml += '<h4 class="font-semibold text-yellow-800 mb-2">Sample Order Calculation</h4>';
            
            // Get current values
            const taxRate = parseFloat(formData.get('settings[tax_rate]') || 0);
            const serviceRate = parseFloat(formData.get('settings[service_charge_rate]') || 0);
            const autoTax = formData.has('settings[auto_apply_tax]');
            const autoService = formData.has('settings[auto_apply_service_charge]');
            const currency = formData.get('settings[currency_symbol]') || '$';
            const taxChargingMethod = formData.get('settings[tax_charging_method]') || 'customer_pays';
            
            // Sample calculation based on tax charging method
            const subtotal = 100.00;
            let tax = 0;
            let displaySubtotal = subtotal;
            
            if (autoTax) {
                if (taxChargingMethod === 'customer_pays') {
                    // Customer pays tax - add tax to final price
                    tax = subtotal * taxRate;
                } else {
                    // Business absorbs tax - tax is included in price
                    tax = subtotal * taxRate / (1 + taxRate);
                    displaySubtotal = subtotal; // Price stays the same
                }
            }
            
            const service = autoService ? subtotal * serviceRate : 0;
            const total = taxChargingMethod === 'customer_pays' ? subtotal + tax + service : subtotal + service;
            
            previewHtml += `
                <div class="text-sm space-y-1">
                    <div class="flex justify-between"><span>Subtotal:</span><span>${currency}${subtotal.toFixed(2)}</span></div>
                    <div class="flex justify-between">
                        <span>Tax (${(taxRate * 100).toFixed(1)}%):</span>
                        <span>${currency}${tax.toFixed(2)} ${taxChargingMethod === 'business_absorbs' ? '(included)' : ''}</span>
                    </div>
                    <div class="flex justify-between"><span>Service (${(serviceRate * 100).toFixed(1)}%):</span><span>${currency}${service.toFixed(2)}</span></div>
                    <div class="flex justify-between font-semibold border-t pt-1"><span>Total:</span><span>${currency}${total.toFixed(2)}</span></div>
                </div>
            `;
            previewHtml += '</div>';
            
            // Add other settings preview
            previewHtml += '<div class="space-y-2 text-sm">';
            previewHtml += `<div><strong>Currency Symbol:</strong> ${currency}</div>`;
            previewHtml += `<div><strong>Tax Charging Method:</strong> ${taxChargingMethod === 'customer_pays' ? '💰 Customer Pays Tax' : '🏢 Business Absorbs Tax'}</div>`;
            previewHtml += `<div><strong>Auto Apply Tax:</strong> ${autoTax ? 'Yes' : 'No'}</div>`;
            previewHtml += `<div><strong>Auto Apply Service:</strong> ${autoService ? 'Yes' : 'No'}</div>`;
            previewHtml += `<div><strong>Max Discount:</strong> ${formData.get('settings[max_discount_percentage]') || 0}%</div>`;
            previewHtml += `<div><strong>Order Timeout:</strong> ${formData.get('settings[order_timeout_minutes]') || 30} minutes</div>`;
            previewHtml += '</div>';
            
            previewContent.innerHTML = previewHtml;
            document.getElementById('previewModal').classList.remove('hidden');
        }
        
        function closePreview() {
            document.getElementById('previewModal').classList.add('hidden');
        }
        
        // Close modal when clicking outside
        document.getElementById('previewModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePreview();
            }
        });
    </script>
</x-app-layout>
