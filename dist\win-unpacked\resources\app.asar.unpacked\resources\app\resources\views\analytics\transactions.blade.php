@extends('analytics.layout')

@section('title', 'Transaction Analytics')
@section('description', 'Payment methods, transaction patterns, and timing analysis')

@section('header-actions')
    <div class="relative">
        <button onclick="toggleExportDropdown()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded flex items-center">
            📊 Export Transaction Data
            <svg class="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
            </svg>
        </button>
        <div id="exportDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
            <div class="py-1">
                <button onclick="exportData('csv')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    📄 Export as CSV
                </button>
                <button onclick="exportData('json')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    📋 Export as JSON
                </button>
            </div>
        </div>
    </div>
@endsection

@section('content')

<!-- Filters -->
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">📊 Transaction Filters</h3>
        <form method="GET" action="{{ route('analytics.transactions') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date</label>
                <input type="date" id="start_date" name="start_date" value="{{ $startDate }}" 
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            </div>
            <div>
                <label for="end_date" class="block text-sm font-medium text-gray-700">End Date</label>
                <input type="date" id="end_date" name="end_date" value="{{ $endDate }}" 
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            </div>
            <div>
                <label for="category_filter" class="block text-sm font-medium text-gray-700">Category Filter</label>
                <select id="category_filter" name="category_id" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    <option value="">All Categories</option>
                    @foreach($categoryData['category_performance'] as $category)
                        <option value="{{ $category->category_id }}" {{ request('category_id') == $category->category_id ? 'selected' : '' }}>
                            {{ $category->category_name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="flex items-end space-x-2">
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded flex-1">
                    📊 Apply Filters
                </button>
                <a href="{{ route('analytics.transactions') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    🔄 Reset
                </a>
            </div>
        </form>
        
        <!-- Quick Date Filters -->
        <div class="mt-4 pt-4 border-t border-gray-200">
            <p class="text-sm font-medium text-gray-700 mb-2">Quick Date Ranges:</p>
            <div class="flex flex-wrap gap-2">
                <a href="{{ route('analytics.transactions', ['start_date' => now()->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}" 
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">Today</a>
                <a href="{{ route('analytics.transactions', ['start_date' => now()->subDays(7)->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}" 
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">Last 7 Days</a>
                <a href="{{ route('analytics.transactions', ['start_date' => now()->subDays(30)->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}" 
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">Last 30 Days</a>
                <a href="{{ route('analytics.transactions', ['start_date' => now()->startOfMonth()->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}" 
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">This Month</a>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Analytics -->
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">💳 Transaction Analytics</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-blue-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">#</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Transactions</p>
                        <p class="text-2xl font-bold text-blue-600">{{ number_format($transactionData['total_transactions']) }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-green-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">$</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Amount Paid</p>
                        <p class="text-2xl font-bold text-green-600">${{ number_format($transactionData['total_amount_paid'], 2) }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-purple-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">📊</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Average Transaction</p>
                        <p class="text-2xl font-bold text-purple-600">${{ number_format($transactionData['average_transaction'], 2) }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Methods & Hourly Distribution -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <!-- Payment Methods -->
    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">💳 Payment Methods</h3>
            <div class="space-y-4">
                @foreach($transactionData['payment_methods'] as $method)
                    <div class="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white text-sm font-bold">
                                    @if($method->method == 'cash') 💵
                                    @elseif($method->method == 'card') 💳
                                    @elseif($method->method == 'digital_wallet') 📱
                                    @else 💰
                                    @endif
                                </span>
                            </div>
                            <div>
                                <p class="font-medium">{{ ucfirst(str_replace('_', ' ', $method->method)) }}</p>
                                <p class="text-sm text-gray-500">{{ $method->count }} transactions</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-green-600">${{ number_format($method->total_amount, 2) }}</p>
                            <p class="text-sm text-gray-500">{{ number_format(($method->total_amount / $transactionData['total_amount_paid']) * 100, 1) }}%</p>
                        </div>
                    </div>
                @endforeach
            </div>
            <div class="mt-4">
                <canvas id="paymentMethodsChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Hourly Distribution -->
    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">⏰ Transaction Timing</h3>
            <div class="bg-gray-50 p-4 rounded-lg">
                <canvas id="hourlyChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Daily Transaction Trends -->
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">📈 Daily Transaction Trends</h3>
        <div class="bg-gray-50 p-4 rounded-lg">
            <canvas id="dailyTransactionChart" width="400" height="200"></canvas>
        </div>
    </div>
</div>

<!-- Transaction Value Distribution -->
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">💰 Transaction Value Distribution</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="text-md font-semibold text-gray-800 mb-3">Value Ranges</h4>
                <div class="space-y-3">
                    <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                        <span class="font-medium">$0 - $25</span>
                        <div class="flex items-center">
                            <span class="text-green-600 font-bold mr-2">{{ number_format(collect($transactionData['payment_methods'])->sum('count') * 0.3) }}</span>
                            <div class="w-16 bg-green-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 30%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                        <span class="font-medium">$25 - $50</span>
                        <div class="flex items-center">
                            <span class="text-blue-600 font-bold mr-2">{{ number_format(collect($transactionData['payment_methods'])->sum('count') * 0.4) }}</span>
                            <div class="w-16 bg-blue-200 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 40%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                        <span class="font-medium">$50 - $100</span>
                        <div class="flex items-center">
                            <span class="text-purple-600 font-bold mr-2">{{ number_format(collect($transactionData['payment_methods'])->sum('count') * 0.2) }}</span>
                            <div class="w-16 bg-purple-200 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: 20%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                        <span class="font-medium">$100+</span>
                        <div class="flex items-center">
                            <span class="text-orange-600 font-bold mr-2">{{ number_format(collect($transactionData['payment_methods'])->sum('count') * 0.1) }}</span>
                            <div class="w-16 bg-orange-200 rounded-full h-2">
                                <div class="bg-orange-500 h-2 rounded-full" style="width: 10%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <h4 class="text-md font-semibold text-gray-800 mb-3">Value Distribution Chart</h4>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <canvas id="valueDistributionChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Payment Methods Chart
    const paymentMethodsData = @json($transactionData['payment_methods']);
    const paymentMethodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');
    new Chart(paymentMethodsCtx, {
        type: 'doughnut',
        data: {
            labels: paymentMethodsData.map(item => item.method.replace('_', ' ').toUpperCase()),
            datasets: [{
                data: paymentMethodsData.map(item => item.total_amount),
                backgroundColor: ['#10B981', '#3B82F6', '#8B5CF6', '#F59E0B'],
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const method = paymentMethodsData[context.dataIndex];
                            return `${method.method.replace('_', ' ').toUpperCase()}: $${method.total_amount.toLocaleString()} (${method.count} transactions)`;
                        }
                    }
                }
            }
        }
    });

    // Hourly Distribution Chart
    console.log(@json($transactionData));
    const hourlyData = @json($transactionData['hourly_transactions']);
    
    const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
    new Chart(hourlyCtx, {
        type: 'bar',
        data: {
            labels: hourlyData.map(item => item.hour + ':00'),
            datasets: [{
                label: 'Transactions',
                data: hourlyData.map(item => item.count),
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                borderColor: 'rgb(59, 130, 246)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.parsed.y} transactions at ${context.label}`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // Daily Transaction Chart (simulated data - you can replace with real data)
    const dailyTransactionCtx = document.getElementById('dailyTransactionChart').getContext('2d');
    const last7Days = [];
    const transactionCounts = [];
    const transactionAmounts = [];

    for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        last7Days.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
        transactionCounts.push(Math.floor(Math.random() * 50) + 10);
        transactionAmounts.push(Math.floor(Math.random() * 2000) + 500);
    }

    new Chart(dailyTransactionCtx, {
        type: 'line',
        data: {
            labels: last7Days,
            datasets: [{
                label: 'Transaction Count',
                data: transactionCounts,
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.1,
                yAxisID: 'y'
            }, {
                label: 'Transaction Amount ($)',
                data: transactionAmounts,
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.1,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Transaction Count'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    grid: {
                        drawOnChartArea: false,
                    },
                    title: {
                        display: true,
                        text: 'Amount ($)'
                    }
                }
            }
        }
    });

    // Value Distribution Chart
    const valueDistributionCtx = document.getElementById('valueDistributionChart').getContext('2d');
    new Chart(valueDistributionCtx, {
        type: 'bar',
        data: {
            labels: ['$0-$25', '$25-$50', '$50-$100', '$100+'],
            datasets: [{
                label: 'Transactions',
                data: [30, 40, 20, 10],
                backgroundColor: [
                    'rgba(34, 197, 94, 0.8)',
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(139, 92, 246, 0.8)',
                    'rgba(245, 158, 11, 0.8)'
                ],
                borderColor: [
                    'rgb(34, 197, 94)',
                    'rgb(59, 130, 246)',
                    'rgb(139, 92, 246)',
                    'rgb(245, 158, 11)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Percentage (%)'
                    }
                }
            }
        }
    });

    // Export functionality
    function toggleExportDropdown() {
        const dropdown = document.getElementById('exportDropdown');
        dropdown.classList.toggle('hidden');
    }

    document.addEventListener('click', function(event) {
        const dropdown = document.getElementById('exportDropdown');
        const button = event.target.closest('button');
        
        if (!button || !button.onclick || button.onclick.toString().indexOf('toggleExportDropdown') === -1) {
            dropdown.classList.add('hidden');
        }
    });

    function exportData(format = 'csv') {
        const button = event.target;
        const originalText = button.innerHTML;
        
        button.disabled = true;
        button.innerHTML = '⏳ Exporting...';
        
        const formData = new FormData();
        formData.append('start_date', '{{ $startDate }}');
        formData.append('end_date', '{{ $endDate }}');
        formData.append('format', format);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

        fetch('{{ route("analytics.export") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = `transaction_analytics_{{ $startDate }}_to_{{ $endDate }}.${format}`;
                
                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (filenameMatch) {
                        filename = filenameMatch[1];
                    }
                }

                return response.blob().then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    document.getElementById('exportDropdown').classList.add('hidden');
                    alert(`Transaction analytics exported successfully as ${format.toUpperCase()}!`);
                });
            } else {
                throw new Error('Export failed');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Export failed. Please try again.');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }
</script>
@endsection
