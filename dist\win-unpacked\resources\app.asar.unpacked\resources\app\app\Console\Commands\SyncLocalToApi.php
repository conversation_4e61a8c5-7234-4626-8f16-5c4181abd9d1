<?php

namespace App\Console\Commands;

use App\Models\MenuCategory;
use App\Models\MenuItem;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class SyncLocalToApi extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:sync-local-to-api {--username=} {--password=} {--dry-run : Show what would be synced without actually doing it}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync local menu data to the external API';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('🔍 DRY RUN MODE - No actual API calls will be made');
            $this->newLine();
        }

        // Get local data
        $this->info('📋 Loading local menu data...');
        $categories = MenuCategory::with('menuItems')->get();
        
        $this->info("Found {$categories->count()} categories with menu items:");
        
        foreach ($categories as $category) {
            $this->line("  📁 {$category->name} ({$category->menuItems->count()} items)");
            foreach ($category->menuItems as $item) {
                $this->line("    - {$item->name} (Rp " . number_format($item->price, 0, ',', '.') . ")");
            }
        }
        
        $this->newLine();
        
        if ($dryRun) {
            $this->info('📤 Data that would be synced to API:');
            
            foreach ($categories as $category) {
                foreach ($category->menuItems as $item) {
                    $apiData = [
                        'name' => $item->name,
                        'description' => $item->description,
                        'price' => $item->price,
                        'category' => [
                            'id' => $category->id,
                            'name' => $category->name
                        ],
                        'is_active' => true,
                        'image_path' => $item->image_path
                    ];
                    
                    $this->line('Item: ' . json_encode($apiData, JSON_PRETTY_PRINT));
                    $this->newLine();
                }
            }
            
            $this->info('💡 To actually sync this data, run the command without --dry-run');
            $this->info('💡 Make sure the API supports creating/updating products');
            
            return Command::SUCCESS;
        }

        // Get credentials
        $username = $this->option('username') ?: $this->ask('Enter API username');
        $password = $this->option('password') ?: $this->secret('Enter API password');
        
        if (!$username || !$password) {
            $this->error('Username and password are required');
            return Command::FAILURE;
        }

        $baseUrl = 'http://viera-filament.test/api/pos';
        
        $this->info('🔐 Authenticating with API...');
        
        try {
            // Authenticate
            $authResponse = Http::timeout(30)
                ->post($baseUrl . '/login', [
                    'username' => $username,
                    'password' => $password,
                ]);

            if (!$authResponse->successful()) {
                $this->error('❌ Authentication failed');
                return Command::FAILURE;
            }

            $authData = $authResponse->json();
            $token = $authData['token'] ?? $authData['access_token'] ?? null;

            if (!$token) {
                $this->error('❌ No token in authentication response');
                return Command::FAILURE;
            }

            $this->info('✅ Authentication successful');
            
            // Check if API has endpoints for creating/updating products
            $this->newLine();
            $this->warn('⚠️  This command assumes the API has endpoints for creating/updating products.');
            $this->warn('⚠️  You may need to check the API documentation for the correct endpoints.');
            $this->newLine();
            
            if (!$this->confirm('Do you want to continue with the sync attempt?')) {
                $this->info('Sync cancelled by user');
                return Command::SUCCESS;
            }
            
            // Try to sync each item
            $this->info('📤 Starting sync...');
            
            foreach ($categories as $category) {
                $this->info("Syncing category: {$category->name}");
                
                foreach ($category->menuItems as $item) {
                    $this->line("  Syncing: {$item->name}");
                    
                    $apiData = [
                        'name' => $item->name,
                        'sku' => $item->sku,
                        'barcode' => $item->barcode,
                        'description' => $item->description,
                        'price' => $item->price,
                        'cost_price' => $item->cost_price,
                        'category_id' => $category->id,
                        'category_name' => $category->name,
                        'stock_quantity' => $item->stock_quantity,
                        'is_active' => $item->is_active,
                        'is_food_item' => $item->is_food_item,
                        'image' => $item->image
                    ];
                    
                    // Try different possible endpoints
                    $endpoints = [
                        '/products',
                        '/sync/products',
                        '/menu-items',
                        '/items'
                    ];
                    
                    $synced = false;
                    foreach ($endpoints as $endpoint) {
                        try {
                            $response = Http::withHeaders([
                                'Authorization' => 'Bearer ' . $token,
                                'Accept' => 'application/json',
                                'Content-Type' => 'application/json',
                            ])
                            ->timeout(30)
                            ->post($baseUrl . $endpoint, $apiData);
                            
                            if ($response->successful()) {
                                $this->info("    ✅ Synced via {$endpoint}");
                                $synced = true;
                                break;
                            }
                        } catch (\Exception $e) {
                            // Continue to next endpoint
                        }
                    }
                    
                    if (!$synced) {
                        $this->error("    ❌ Failed to sync {$item->name}");
                        $this->error("    No working endpoint found. API may not support product creation.");
                    }
                }
            }

        } catch (\Exception $e) {
            $this->error('❌ Error: ' . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
