<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register custom API session guard
        \Illuminate\Support\Facades\Auth::extend('api_session', function ($app) {
            return new \App\Guards\ApiSessionGuard($app['request']);
        });
    }
}
