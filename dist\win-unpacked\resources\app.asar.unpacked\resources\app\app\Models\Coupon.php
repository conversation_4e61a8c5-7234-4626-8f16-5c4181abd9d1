<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Coupon extends Model
{
    protected $fillable = [
        'code',
        'name',
        'description',
        'type',
        'value',
        'minimum_amount',
        'usage_limit',
        'used_count',
        'is_active',
        'valid_from',
        'valid_until',
    ];

    protected $casts = [
        'valid_from' => 'datetime',
        'valid_until' => 'datetime',
        'is_active' => 'boolean',
        'value' => 'decimal:2',
        'minimum_amount' => 'decimal:2',
    ];

    /**
     * Check if coupon is valid for use
     */
    public function isValid($orderAmount = 0)
    {
        $now = Carbon::now();

        return $this->is_active &&
               $now->between($this->valid_from, $this->valid_until) &&
               ($this->usage_limit === null || $this->used_count < $this->usage_limit) &&
               $orderAmount >= $this->minimum_amount;
    }

    /**
     * Calculate discount amount for given order total
     */
    public function calculateDiscount($orderAmount)
    {
        if (!$this->isValid($orderAmount)) {
            return 0;
        }

        if ($this->type === 'percentage') {
            return ($orderAmount * $this->value) / 100;
        } else {
            return min($this->value, $orderAmount);
        }
    }

    /**
     * Mark coupon as used
     */
    public function markAsUsed()
    {
        $this->increment('used_count');
    }
}
