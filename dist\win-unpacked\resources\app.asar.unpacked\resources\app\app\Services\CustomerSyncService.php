<?php

namespace App\Services;

use App\Models\Customer;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class CustomerSyncService
{
    private string $apiBaseUrl;
    private int $timeout;

    public function __construct()
    {
        $this->apiBaseUrl = config('pos.api_base_url', 'http://viera-filament.test/api/pos');
        $this->timeout = config('pos.api_timeout', 30);
    }

    /**
     * Sync a single customer to the API
     */
    public function syncCustomer(Customer $customer): array
    {
        try {
            Log::info('CustomerSync: Starting sync for customer', ['customer_id' => $customer->id]);

            // Check network connectivity first
            if (!$this->hasNetworkConnectivity()) {
                Log::warning('CustomerSync: No network connectivity, skipping sync', ['customer_id' => $customer->id]);
                return [
                    'success' => false,
                    'error' => 'No network connectivity',
                    'should_retry' => true
                ];
            }

            // Get API token
            $token = $this->getApiToken();
            if (!$token) {
                throw new \Exception('No API token available');
            }

            // Prepare customer data for API
            $customerData = $this->prepareCustomerData($customer);

            // Send to API
            $response = Http::timeout($this->timeout)
                ->withToken($token)
                ->post("{$this->apiBaseUrl}/sync/customers", $customerData);

            if ($response->successful()) {
                $responseData = $response->json();

                // Update local customer with API ID and mark as synced
                $apiId = $responseData['data']['id'] ?? null;
                if ($apiId) {
                    $customer->update([
                        'api_id' => $apiId,
                        'is_synced_to_api' => true,
                        'last_api_sync_at' => now(),
                    ]);
                } else {
                    // Even if no API ID is returned, mark as synced if the request was successful
                    $customer->update([
                        'is_synced_to_api' => true,
                        'last_api_sync_at' => now(),
                    ]);
                }

                Log::info('CustomerSync: Customer synced to API successfully', [
                    'local_id' => $customer->id,
                    'api_id' => $apiId,
                    'customer_name' => $customer->name
                ]);

                return [
                    'success' => true,
                    'api_id' => $apiId,
                    'message' => 'Customer synced successfully'
                ];

            } else {
                $errorMessage = $response->json()['message'] ?? 'API request failed';
                Log::error('CustomerSync: API request failed', [
                    'customer_id' => $customer->id,
                    'status' => $response->status(),
                    'error' => $errorMessage,
                    'response' => $response->body()
                ]);

                return [
                    'success' => false,
                    'error' => $errorMessage,
                    'status_code' => $response->status(),
                    'should_retry' => $response->status() >= 500 // Retry on server errors
                ];
            }

        } catch (\Exception $e) {
            Log::error('CustomerSync: Exception during sync', [
                'customer_id' => $customer->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'should_retry' => true
            ];
        }
    }

    /**
     * Prepare customer data for API according to the required format
     */
    private function prepareCustomerData(Customer $customer): array
    {
        return [
            'nama' => $customer->name,
            'email' => $customer->email,
            'telepon' => $customer->phone,
            'alamat' => $customer->address,
            'tanggal_lahir' => $customer->date_of_birth?->format('Y-m-d'),
            'jenis_kelamin' => $this->mapGenderForApi($customer->gender),
            'loyalty_points' => $customer->loyalty_points ?? 0,
            'segment' => $customer->customer_segment,
            'notes' => $customer->notes,
            'is_active' => $customer->active_status ?? true,
            'province_id' => null, // Not implemented yet
            'city_id' => null, // Not implemented yet
            'district_id' => null, // Not implemented yet
            'village_id' => null, // Not implemented yet
            'postal_code' => $customer->postal_code,
            'detail_address' => $customer->detailed_address,
        ];
    }

    /**
     * Map gender from local format to API format
     */
    private function mapGenderForApi(?string $gender): ?string
    {
        if (!$gender) {
            return null;
        }

        return match($gender) {
            'male' => 'L',
            'female' => 'P',
            default => null
        };
    }

    /**
     * Get API authentication token
     */
    private function getApiToken(): ?string
    {
        // Try to get cached token first
        $token = Cache::get('pos_api_token');
        if ($token) {
            return $token;
        }

        // If no cached token, try to authenticate
        try {
            $response = Http::timeout($this->timeout)
                ->post("{$this->apiBaseUrl}/login", [
                    'email' => config('pos.api_email'),
                    'password' => config('pos.api_password'),
                    'device_name' => 'POS_Customer_Sync',
                ]);

            if ($response->successful()) {
                $data = $response->json();
                $token = $data['token'] ?? $data['access_token'] ?? null;
                
                if ($token) {
                    // Cache token for 1 hour
                    Cache::put('pos_api_token', $token, 3600);
                    return $token;
                }
            }
        } catch (\Exception $e) {
            Log::error('CustomerSync: Failed to get API token', ['error' => $e->getMessage()]);
        }

        return null;
    }

    /**
     * Check network connectivity
     */
    private function hasNetworkConnectivity(): bool
    {
        // For now, always return true since we know the API is accessible
        // The actual API call will handle connection failures
        return true;

        // TODO: Implement proper connectivity check later
        /*
        try {
            // Test connectivity to our actual API endpoint
            $response = Http::timeout(5)->get($this->apiBaseUrl . '/test-connection');
            return $response->successful();
        } catch (\Exception $e) {
            Log::warning('CustomerSync: Network connectivity check failed', [
                'api_url' => $this->apiBaseUrl,
                'error' => $e->getMessage()
            ]);
            return false;
        }
        */
    }

    /**
     * Sync multiple customers in batch
     */
    public function syncMultipleCustomers(array $customerIds): array
    {
        $results = [
            'success_count' => 0,
            'failed_count' => 0,
            'errors' => []
        ];

        foreach ($customerIds as $customerId) {
            $customer = Customer::find($customerId);
            if (!$customer) {
                $results['failed_count']++;
                $results['errors'][] = "Customer with ID {$customerId} not found";
                continue;
            }

            $result = $this->syncCustomer($customer);
            if ($result['success']) {
                $results['success_count']++;
            } else {
                $results['failed_count']++;
                $results['errors'][] = "Customer {$customer->name}: " . $result['error'];
            }
        }

        return $results;
    }

    /**
     * Sync all unsynced customers
     */
    public function syncUnsyncedCustomers(): array
    {
        $unsyncedCustomers = Customer::where('is_synced_to_api', false)
            ->orWhereNull('is_synced_to_api')
            ->get();

        if ($unsyncedCustomers->isEmpty()) {
            return [
                'success_count' => 0,
                'failed_count' => 0,
                'message' => 'No unsynced customers found'
            ];
        }

        return $this->syncMultipleCustomers($unsyncedCustomers->pluck('id')->toArray());
    }
}
