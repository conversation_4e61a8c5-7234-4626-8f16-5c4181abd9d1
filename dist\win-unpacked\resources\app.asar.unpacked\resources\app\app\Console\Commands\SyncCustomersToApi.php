<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Customer;
use App\Services\CustomerSyncService;

class SyncCustomersToApi extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'customers:sync-to-api 
                            {--all : Sync all customers regardless of sync status}
                            {--force : Force sync even if already synced}
                            {--limit=10 : Limit number of customers to sync}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync customers to external API';

    private CustomerSyncService $customerSyncService;

    public function __construct(CustomerSyncService $customerSyncService)
    {
        parent::__construct();
        $this->customerSyncService = $customerSyncService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting customer sync to API...');
        $this->newLine();

        try {
            // Build query based on options
            $query = Customer::query();
            
            if (!$this->option('all') && !$this->option('force')) {
                // Only sync unsynced customers by default
                $query->where(function($q) {
                    $q->where('is_synced_to_api', false)
                      ->orWhereNull('is_synced_to_api');
                });
            }

            if ($this->option('limit')) {
                $query->limit((int) $this->option('limit'));
            }

            $customers = $query->orderBy('created_at', 'desc')->get();

            if ($customers->isEmpty()) {
                $this->info('✅ No customers found to sync.');
                return Command::SUCCESS;
            }

            $this->info("Found {$customers->count()} customer(s) to sync.");
            $this->newLine();

            $successCount = 0;
            $failedCount = 0;
            $errors = [];

            // Create progress bar
            $progressBar = $this->output->createProgressBar($customers->count());
            $progressBar->setFormat('verbose');

            foreach ($customers as $customer) {
                $progressBar->advance();
                
                $this->line("\n📋 Syncing: {$customer->name} (ID: {$customer->id})");
                
                $result = $this->customerSyncService->syncCustomer($customer);
                
                if ($result['success']) {
                    $successCount++;
                    $this->info("    ✅ Synced successfully" . 
                        ($result['api_id'] ? " (API ID: {$result['api_id']})" : ""));
                } else {
                    $failedCount++;
                    $error = "Customer '{$customer->name}': " . ($result['error'] ?? 'Unknown error');
                    $errors[] = $error;
                    $this->error("    ❌ Failed: " . ($result['error'] ?? 'Unknown error'));
                    
                    if (isset($result['should_retry']) && $result['should_retry']) {
                        $this->warn("    🔄 This customer should be retried later");
                    }
                }
            }

            $progressBar->finish();
            $this->newLine(2);

            // Summary
            $this->info('📊 Sync Summary:');
            $this->table(
                ['Metric', 'Count'],
                [
                    ['Total Processed', $customers->count()],
                    ['Successfully Synced', $successCount],
                    ['Failed', $failedCount],
                    ['Success Rate', $customers->count() > 0 ? round(($successCount / $customers->count()) * 100, 2) . '%' : '0%']
                ]
            );

            if (!empty($errors)) {
                $this->newLine();
                $this->error('❌ Errors encountered:');
                foreach ($errors as $error) {
                    $this->line("  • {$error}");
                }
            }

            if ($failedCount > 0) {
                $this->newLine();
                $this->warn('💡 Tips:');
                $this->line('  • Check your internet connection');
                $this->line('  • Verify API credentials in .env file');
                $this->line('  • Run with --force to retry failed syncs');
                $this->line('  • Check logs for detailed error information');
            }

            return $failedCount > 0 ? Command::FAILURE : Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ Error: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
