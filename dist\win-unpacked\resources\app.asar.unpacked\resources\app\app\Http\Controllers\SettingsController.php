<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Customer;
use App\Models\MenuItem;
use App\Models\MenuCategory;
use App\Models\TableLayout;
use App\Models\Order;

class SettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth']);
    }

    /**
     * Display the main settings page
     */
    public function index()
    {
        // Get some basic statistics for the settings dashboard
        $stats = [
            'customers' => Customer::count(),
            'menu_items' => MenuItem::count(),
            'menu_categories' => MenuCategory::count(),
            'tables' => TableLayout::count(),
            'total_orders' => Order::count(),
            'pending_orders' => Order::where('status', 'Pending')->count(),
        ];

        return view('settings.index', compact('stats'));
    }
}
