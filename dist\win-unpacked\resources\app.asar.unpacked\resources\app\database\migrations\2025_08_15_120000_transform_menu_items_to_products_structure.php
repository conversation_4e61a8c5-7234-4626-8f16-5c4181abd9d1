<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, add new columns
        Schema::table('menu_items', function (Blueprint $table) {
            $table->string('sku')->nullable()->unique()->after('name')->comment('Stock Keeping Unit');
            $table->string('barcode')->nullable()->unique()->after('sku')->comment('Barcode produk');
            $table->decimal('cost_price', 15, 2)->nullable()->after('price')->comment('Harga beli/cost');
            $table->integer('stock_quantity')->default(0)->after('cost_price')->comment('Jumlah stok');
            $table->boolean('is_food_item')->default(true)->after('is_available')->comment('Apakah produk makanan/minuman');
            $table->softDeletes()->after('updated_at');
        });

        // Rename columns (separate operations for better compatibility)
        Schema::table('menu_items', function (Blueprint $table) {
            $table->renameColumn('image_path', 'image');
        });

        Schema::table('menu_items', function (Blueprint $table) {
            $table->renameColumn('is_available', 'is_active');
        });

        // Modify existing columns and add indexes
        Schema::table('menu_items', function (Blueprint $table) {
            $table->decimal('price', 15, 2)->change();

            // Add indexes for better performance
            $table->index('name');
            $table->index('is_active');
            $table->index('is_food_item');
            $table->index('stock_quantity');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes first
        Schema::table('menu_items', function (Blueprint $table) {
            $table->dropIndex(['name']);
            $table->dropIndex(['is_active']);
            $table->dropIndex(['is_food_item']);
            $table->dropIndex(['stock_quantity']);
        });

        // Rename columns back
        Schema::table('menu_items', function (Blueprint $table) {
            $table->renameColumn('is_active', 'is_available');
        });

        Schema::table('menu_items', function (Blueprint $table) {
            $table->renameColumn('image', 'image_path');
        });

        // Drop new columns and revert changes
        Schema::table('menu_items', function (Blueprint $table) {
            $table->dropColumn([
                'sku',
                'barcode',
                'cost_price',
                'stock_quantity',
                'is_food_item',
                'deleted_at'
            ]);

            // Revert column changes
            $table->decimal('price', 10, 2)->change();
        });
    }
};
