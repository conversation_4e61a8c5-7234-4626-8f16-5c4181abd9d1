<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Order;
use App\Models\OrderSetting;

class RecalculateOrderTotals extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:recalculate-totals {--order-id= : Specific order ID to recalculate}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recalculate order totals based on current tax and service charge settings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $orderId = $this->option('order-id');
        
        if ($orderId) {
            $orders = Order::where('id', $orderId)->get();
            if ($orders->isEmpty()) {
                $this->error("Order #{$orderId} not found.");
                return 1;
            }
        } else {
            $orders = Order::whereIn('status', ['Pending', 'In Progress', 'Preparing', 'Ready', 'Completed'])->get();
        }

        if ($orders->isEmpty()) {
            $this->info('No orders found to recalculate.');
            return 0;
        }

        $this->info("Recalculating totals for " . $orders->count() . " order(s)...");

        // Get current settings
        $taxRate = OrderSetting::get('tax_rate', 0.10);
        $taxChargingMethod = OrderSetting::get('tax_charging_method', 'customer_pays');
        $serviceChargeRate = OrderSetting::get('service_charge_rate', 0.00);
        $autoApplyTax = OrderSetting::get('auto_apply_tax', true);
        $autoApplyService = OrderSetting::get('auto_apply_service_charge', false);

        $this->info("Using settings:");
        $this->info("- Tax Rate: " . ($taxRate * 100) . "%");
        $this->info("- Tax Charging Method: " . $taxChargingMethod);
        $this->info("- Service Charge Rate: " . ($serviceChargeRate * 100) . "%");
        $this->info("- Auto Apply Tax: " . ($autoApplyTax ? 'Yes' : 'No'));
        $this->info("- Auto Apply Service: " . ($autoApplyService ? 'Yes' : 'No'));

        $progressBar = $this->output->createProgressBar($orders->count());
        $progressBar->start();

        foreach ($orders as $order) {
            $order->load(['orderItems', 'activeDiscounts']);

            // Calculate subtotal
            $subtotal = $order->orderItems->sum(function ($item) {
                return $item->quantity * $item->price_at_time;
            });

            // Get total discount amount
            $totalDiscountAmount = $order->activeDiscounts->sum('amount');

            // Calculate discounted subtotal
            $discountedSubtotal = max(0, $subtotal - $totalDiscountAmount);

            // Calculate tax amount based on charging method
            $taxAmount = 0;
            if ($autoApplyTax) {
                if ($taxChargingMethod === 'customer_pays') {
                    // Customer pays tax - add tax to the final amount
                    $taxAmount = $discountedSubtotal * $taxRate;
                } else {
                    // Business absorbs tax - tax is included in the price, so no additional tax
                    $taxAmount = 0;
                }
            }

            // Calculate service charge on discounted subtotal
            $serviceChargeAmount = $autoApplyService ? ($discountedSubtotal * $serviceChargeRate) : 0;

            // Calculate total
            $total = $discountedSubtotal + $taxAmount + $serviceChargeAmount;

            // Update order
            $order->update([
                'subtotal_amount' => $subtotal,
                'discount_amount' => $totalDiscountAmount,
                'tax_amount' => $taxAmount,
                'service_charge_amount' => $serviceChargeAmount,
                'total_amount' => $total,
            ]);

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        $this->info("Successfully recalculated totals for " . $orders->count() . " order(s).");

        return 0;
    }
}