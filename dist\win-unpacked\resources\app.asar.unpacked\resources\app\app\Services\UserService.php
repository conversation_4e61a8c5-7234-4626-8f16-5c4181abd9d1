<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;

class UserService
{
    private $userApiService;

    public function __construct()
    {
        // We'll use the existing PosApiService for now, but this could be a separate UserApiService
        $this->userApiService = app(PosApiService::class);
    }

    /**
     * Synchronize users from API to local database with complete replacement
     */
    public function syncUsersToDatabase(bool $forceRefresh = false): array
    {
        // Prevent concurrent syncs
        $lockKey = 'user_sync_lock';
        if (Cache::has($lockKey)) {
            Log::warning('UserService: Sync already in progress, skipping');
            return [
                'success' => false,
                'error' => 'Sync already in progress. Please wait for the current sync to complete.',
                'stats' => [
                    'users_created' => 0,
                    'users_updated' => 0,
                ]
            ];
        }

        // Set sync lock (expires in 5 minutes)
        Cache::put($lockKey, true, 300);

        Log::info('UserService: Starting user synchronization', [
            'force_refresh' => $forceRefresh,
            'lock_set' => true
        ]);

        try {
            // Get fresh data from API
            $apiResult = $this->fetchUsersFromApi();

            if (!$apiResult['success']) {
                Log::error('UserService: API fetch failed during sync', [
                    'error' => $apiResult['error'] ?? 'Unknown error'
                ]);

                return [
                    'success' => false,
                    'error' => 'Failed to fetch data from API: ' . ($apiResult['error'] ?? 'Unknown error'),
                    'stats' => [
                        'users_created' => 0,
                        'users_updated' => 0,
                    ]
                ];
            }

            $users = $apiResult['users'];

            if (empty($users)) {
                Log::info('UserService: No users found in API response');
                return [
                    'success' => true,
                    'message' => 'No users found in API',
                    'stats' => [
                        'users_created' => 0,
                        'users_updated' => 0,
                    ]
                ];
            }

            $result = DB::transaction(function () use ($users) {
                return $this->performDatabaseSync($users);
            });

            return $result;

        } catch (\Exception $e) {
            Log::error('UserService: Sync failed with exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'Sync failed: ' . $e->getMessage(),
                'stats' => [
                    'users_created' => 0,
                    'users_updated' => 0,
                ]
            ];
        } finally {
            // Always release the sync lock
            Cache::forget($lockKey);
            Log::info('UserService: Sync lock released');
        }
    }

    /**
     * Fetch users from API
     */
    private function fetchUsersFromApi(): array
    {
        try {
            // This would need to be implemented based on your API structure
            // For now, returning a placeholder
            Log::info('UserService: Fetching users from API');
            
            // You would implement the actual API call here
            // Example: $response = Http::get($apiUrl . '/users');
            
            return [
                'success' => false,
                'error' => 'User API endpoint not implemented yet'
            ];
            
        } catch (\Exception $e) {
            Log::error('UserService: Failed to fetch users from API', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Perform complete database replacement with API data
     */
    private function performDatabaseSync(array $users): array
    {
        Log::info('UserService: Starting complete data replacement', [
            'users_count' => count($users)
        ]);

        $stats = [
            'users_created' => 0,
            'users_updated' => 0,
        ];

        // Step 1: Delete all existing users (except super admin)
        Log::info('UserService: Deleting all existing users');
        
        // Keep super admin or system users (adjust based on your needs)
        $systemUsers = ['<EMAIL>', '<EMAIL>'];
        $deletedCount = User::whereNotIn('email', $systemUsers)->count();
        User::whereNotIn('email', $systemUsers)->delete();
        
        Log::info('UserService: Deleted existing users', ['count' => $deletedCount]);

        // Step 2: Insert fresh users from API
        Log::info('UserService: Inserting fresh user data from API');
        
        foreach ($users as $userData) {
            // Skip if this user email matches system users
            $userEmail = $userData['email'] ?? '<EMAIL>';
            if (in_array(strtolower($userEmail), array_map('strtolower', $systemUsers))) {
                continue;
            }

            $user = User::create([
                'name' => $userData['name'] ?? $userData['nama'] ?? 'Unknown User',
                'email' => $userEmail,
                'password' => Hash::make($userData['password'] ?? 'defaultpassword'),
                'role' => $userData['role'] ?? 'karyawan',
                'jabatan' => $userData['jabatan'] ?? null,
                'api_id' => $userData['id'] ?? null,
                'email_verified_at' => now(), // Auto-verify API users
            ]);

            $stats['users_created']++;
            Log::debug('UserService: Created user', [
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role
            ]);
        }

        Log::info('UserService: Complete data replacement completed successfully', $stats);

        return [
            'success' => true,
            'message' => 'Users synchronized successfully (complete replacement)',
            'stats' => $stats,
            'processed_users' => count($users)
        ];
    }

    /**
     * Force sync and update cache timestamp
     */
    public function forceSyncFromApi(): array
    {
        $result = $this->syncUsersToDatabase(true);

        if ($result['success']) {
            Cache::put('user_last_sync', now()->format('Y-m-d H:i:s'), 86400);
        }

        return $result;
    }

    /**
     * Get sync status and statistics
     */
    public function getSyncStatus(): array
    {
        $localStats = $this->getLocalDatabaseStats();

        return [
            'api_connected' => false, // Would need to implement API connection test
            'api_status' => 'User API not implemented',
            'last_sync' => Cache::get('user_last_sync', 'Never'),
            'local_users_count' => $localStats['total_users'],
            'system_users_count' => $localStats['system_users'],
        ];
    }

    /**
     * Get local database statistics
     */
    private function getLocalDatabaseStats(): array
    {
        $systemUsers = ['<EMAIL>', '<EMAIL>'];
        
        return [
            'total_users' => User::count(),
            'system_users' => User::whereIn('email', $systemUsers)->count(),
            'api_users' => User::whereNotNull('api_id')->count(),
        ];
    }

    /**
     * Clear user-related caches
     */
    public function clearCache(): void
    {
        // Clear any user-related caches if you have them
        Log::info('UserService: Cache cleared');
    }
}
