<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Purchase extends Model
{
    use HasFactory;

    protected $fillable = [
        'supplier_id',
        'purchase_date',
    ];

    protected function casts(): array
    {
        return [
            'purchase_date' => 'datetime',
        ];
    }

    /**
     * Get the supplier for this purchase.
     */
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get the purchase items for this purchase.
     */
    public function purchaseItems()
    {
        return $this->hasMany(PurchaseItem::class);
    }

    /**
     * Get the ingredients for this purchase.
     */
    public function ingredients()
    {
        return $this->belongsToMany(Ingredient::class, 'purchase_items')
                    ->withPivot('quantity', 'unit_price')
                    ->withTimestamps();
    }

    /**
     * Get the total amount for this purchase.
     */
    public function getTotalAmountAttribute()
    {
        return $this->purchaseItems->sum(function ($item) {
            return $item->quantity * $item->unit_price;
        });
    }
}
