<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class CustomerApiService
{
    private string $baseUrl;
    private int $timeout;
    private int $retryAttempts;

    public function __construct()
    {
        $this->baseUrl = config('pos.api_base_url', 'http://viera-filament.test/api/pos');
        $this->timeout = config('pos.customer_api_timeout', 60); // Use longer timeout for customers
        $this->retryAttempts = config('pos.max_retry_attempts', 3);
    }

    /**
     * Get cached authentication token or authenticate if needed
     */
    private function getAuthToken(): ?string
    {
        // First try to get token from session (if user is logged in)
        if (session()->has('api_token')) {
            $sessionToken = session('api_token');
            if ($sessionToken) {
                return $sessionToken;
            }
        }

        // Fallback to cached token for background processes
        $cachedToken = Cache::get('pos_api_token');
        if ($cachedToken) {
            return $cachedToken;
        }

        return null;
    }

    /**
     * Check if API authentication is available
     */
    private function isAuthenticated(): bool
    {
        $token = $this->getAuthToken();
        return !empty($token);
    }

    /**
     * Make authenticated HTTP request with retry logic
     */
    private function makeRequest(string $method, string $endpoint, array $data = []): array
    {
        if (!$this->isAuthenticated()) {
            return [
                'success' => false,
                'error' => 'API authentication required'
            ];
        }

        $token = $this->getAuthToken();
        $url = $this->baseUrl . $endpoint;

        for ($attempt = 1; $attempt <= $this->retryAttempts; $attempt++) {
            try {
                Log::debug("CustomerApiService: Making {$method} request to {$url} (attempt {$attempt})");

                $response = Http::timeout($this->timeout)
                    ->withHeaders([
                        'Authorization' => 'Bearer ' . $token,
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json',
                    ])
                    ->$method($url, $data);

                if ($response->successful()) {
                    $responseData = $response->json();
                    Log::debug("CustomerApiService: Request successful", ['response' => $responseData]);
                    
                    return [
                        'success' => true,
                        'data' => $responseData
                    ];
                }

                // Handle authentication errors
                if ($response->status() === 401) {
                    Log::warning("CustomerApiService: Authentication failed, clearing session");
                    session()->forget(['api_token', 'api_authenticated']);
                    
                    return [
                        'success' => false,
                        'error' => 'Authentication failed. Please login again.',
                        'status_code' => 401
                    ];
                }

                Log::warning("CustomerApiService: Request failed", [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'attempt' => $attempt
                ]);

                if ($attempt === $this->retryAttempts) {
                    return [
                        'success' => false,
                        'error' => 'API request failed: ' . $response->body(),
                        'status_code' => $response->status()
                    ];
                }

                // Wait before retry (exponential backoff)
                sleep(pow(2, $attempt - 1));

            } catch (\Exception $e) {
                Log::error("CustomerApiService: Request exception", [
                    'message' => $e->getMessage(),
                    'attempt' => $attempt
                ]);

                if ($attempt === $this->retryAttempts) {
                    return [
                        'success' => false,
                        'error' => 'Network error: ' . $e->getMessage()
                    ];
                }

                sleep(pow(2, $attempt - 1));
            }
        }

        return [
            'success' => false,
            'error' => 'Maximum retry attempts exceeded'
        ];
    }

    /**
     * Fetch all customers from API
     */
    public function fetchCustomers(): array
    {
        Log::info('CustomerApiService: Fetching customers from API');

        $result = $this->makeRequest('get', '/sync/customers');

        if (!$result['success']) {
            return $result;
        }

        // Transform API response to expected format
        $customers = $result['data']['customers'] ?? $result['data'] ?? [];

        if (!is_array($customers)) {
            Log::error('CustomerApiService: Invalid API response format', ['data' => $result['data']]);
            return [
                'success' => false,
                'error' => 'Invalid API response format'
            ];
        }

        // Transform API field names to match our local structure
        $transformedCustomers = array_map(function ($customer) {
            return [
                'id' => $customer['id'] ?? null,
                'name' => $customer['nama'] ?? $customer['name'] ?? 'Unknown Customer',
                'phone' => $customer['telepon'] ?? $customer['phone'] ?? '',
                'email' => $customer['email'] ?? '',
                'address' => $customer['address'] ?? null,
                'points' => $customer['loyalty_points'] ?? $customer['points'] ?? 0,
            ];
        }, $customers);

        Log::info('CustomerApiService: Successfully fetched customers', ['count' => count($transformedCustomers)]);

        return [
            'success' => true,
            'customers' => $transformedCustomers,
            'count' => count($transformedCustomers)
        ];
    }

    /**
     * Create customer via API
     */
    public function createCustomer(array $customerData): array
    {
        Log::info('CustomerApiService: Creating customer via API', ['data' => $customerData]);

        // Transform field names for API
        $apiData = $this->transformForApi($customerData);

        return $this->makeRequest('post', '/sync/customers', $apiData);
    }

    /**
     * Update customer via API
     */
    public function updateCustomer(string $apiId, array $customerData): array
    {
        Log::info('CustomerApiService: Updating customer via API', ['api_id' => $apiId, 'data' => $customerData]);

        // Transform field names for API
        $apiData = $this->transformForApi($customerData);

        return $this->makeRequest('put', "/sync/customers/{$apiId}", $apiData);
    }

    /**
     * Transform customer data field names for API
     */
    private function transformForApi(array $customerData): array
    {
        return [
            'nama' => $customerData['name'] ?? '',
            'telepon' => $customerData['phone'] ?? '',
            'email' => $customerData['email'] ?? '',
            'address' => $customerData['address'] ?? null,
            'loyalty_points' => $customerData['points'] ?? 0,
        ];
    }

    /**
     * Delete customer via API
     */
    public function deleteCustomer(string $apiId): array
    {
        Log::info('CustomerApiService: Deleting customer via API', ['api_id' => $apiId]);

        return $this->makeRequest('delete', "/sync/customers/{$apiId}");
    }

    /**
     * Test API connection
     */
    public function testConnection(): array
    {
        Log::info('CustomerApiService: Testing API connection');

        if (!$this->isAuthenticated()) {
            return [
                'success' => false,
                'message' => 'Not authenticated'
            ];
        }

        $result = $this->makeRequest('get', '/sync/customers?limit=1');

        if ($result['success']) {
            return [
                'success' => true,
                'message' => 'Customer API connection successful'
            ];
        }

        return [
            'success' => false,
            'message' => $result['error'] ?? 'Connection failed'
        ];
    }

    /**
     * Sync local customer to API
     */
    public function syncCustomerToApi(array $customerData): array
    {
        Log::info('CustomerApiService: Syncing customer to API', ['customer' => $customerData['name'] ?? 'Unknown']);

        // If customer has api_id, update; otherwise create
        if (!empty($customerData['api_id'])) {
            return $this->updateCustomer($customerData['api_id'], $customerData);
        } else {
            return $this->createCustomer($customerData);
        }
    }
}
