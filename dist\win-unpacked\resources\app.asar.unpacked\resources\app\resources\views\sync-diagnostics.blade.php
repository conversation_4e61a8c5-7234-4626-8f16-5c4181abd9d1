<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sync Diagnostics - POS System</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">🔧 Sync Diagnostics</h1>
                <p class="mt-2 text-gray-600">Monitor and troubleshoot product synchronization issues</p>
                <div class="mt-4">
                    <a href="{{ route('pos.index') }}" class="text-blue-600 hover:text-blue-800">← Back to POS</a>
                </div>
            </div>

            <!-- API Status Card -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">🌐 API Connection Status</h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center">
                            <div class="text-2xl mb-2">
                                @if($syncStatus['api_connected'])
                                    <span class="text-green-500">✅</span>
                                @else
                                    <span class="text-red-500">❌</span>
                                @endif
                            </div>
                            <div class="text-sm font-medium text-gray-900">API Status</div>
                            <div class="text-xs text-gray-500">
                                {{ $syncStatus['api_connected'] ? 'Connected' : 'Disconnected' }}
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-2">🕒</div>
                            <div class="text-sm font-medium text-gray-900">Last Sync</div>
                            <div class="text-xs text-gray-500">{{ $syncStatus['last_sync'] }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-2">
                                @if($stats['cache_status']['sync_lock_active'])
                                    <span class="text-yellow-500">🔒</span>
                                @else
                                    <span class="text-green-500">🔓</span>
                                @endif
                            </div>
                            <div class="text-sm font-medium text-gray-900">Sync Lock</div>
                            <div class="text-xs text-gray-500">
                                {{ $stats['cache_status']['sync_lock_active'] ? 'Active' : 'Available' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Database Statistics -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">📊 Database Statistics</h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600">{{ $stats['categories'] }}</div>
                            <div class="text-sm text-blue-800">Categories</div>
                        </div>
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <div class="text-2xl font-bold text-green-600">{{ $stats['items'] }}</div>
                            <div class="text-sm text-green-800">Total Items</div>
                        </div>
                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <div class="text-2xl font-bold text-purple-600">{{ $stats['items_with_api_id'] }}</div>
                            <div class="text-sm text-purple-800">With API ID</div>
                        </div>
                        <div class="text-center p-4 bg-orange-50 rounded-lg">
                            <div class="text-2xl font-bold text-orange-600">{{ $stats['items_without_api_id'] }}</div>
                            <div class="text-sm text-orange-800">Without API ID</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cache Status -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">💾 Cache Status</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-700">Product Cache</span>
                            <span class="px-2 py-1 text-xs rounded-full {{ $stats['cache_status']['product_cache_exists'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $stats['cache_status']['product_cache_exists'] ? 'Active' : 'Empty' }}
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-700">Sync Lock</span>
                            <span class="px-2 py-1 text-xs rounded-full {{ $stats['cache_status']['sync_lock_active'] ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800' }}">
                                {{ $stats['cache_status']['sync_lock_active'] ? 'Locked' : 'Available' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">🛠️ Diagnostic Actions</h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <button onclick="testSync()" 
                                class="flex items-center justify-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-all duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                            </svg>
                            Test Sync
                        </button>
                        <button onclick="clearCache()" 
                                class="flex items-center justify-center px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg text-sm font-medium transition-all duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                            Clear Cache
                        </button>
                        <button onclick="window.location.reload()" 
                                class="flex items-center justify-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-all duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                            </svg>
                            Refresh Page
                        </button>
                    </div>
                </div>
            </div>

            <!-- Log Output -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">📝 Diagnostic Log</h2>
                </div>
                <div class="p-6">
                    <div id="diagnostic-log" class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm h-64 overflow-y-auto">
                        <div class="text-gray-500">Ready for diagnostics...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('diagnostic-log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'info': 'text-green-400',
                'error': 'text-red-400',
                'warning': 'text-yellow-400',
                'success': 'text-blue-400'
            };
            
            const logEntry = document.createElement('div');
            logEntry.className = colors[type] || 'text-green-400';
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        async function testSync() {
            log('Starting sync test...', 'info');
            
            try {
                const response = await fetch('{{ route("pos.sync-products") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const result = await response.json();
                
                if (result.success) {
                    log(`✅ Sync successful: ${result.message}`, 'success');
                    if (result.stats) {
                        log(`📊 Stats: Created ${result.stats.categories_created} categories, ${result.stats.items_created} items`, 'info');
                        log(`📊 Stats: Updated ${result.stats.categories_updated} categories, ${result.stats.items_updated} items`, 'info');
                    }
                } else {
                    log(`❌ Sync failed: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`💥 Network error: ${error.message}`, 'error');
            }
        }

        async function clearCache() {
            log('Clearing cache...', 'warning');
            // This would need a backend endpoint to clear cache
            log('⚠️ Cache clearing requires backend implementation', 'warning');
        }

        // Auto-refresh every 30 seconds
        setInterval(() => {
            log('Auto-refreshing page data...', 'info');
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>
