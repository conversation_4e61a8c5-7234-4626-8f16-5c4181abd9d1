<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    🏷️ Tax Settings
                </h2>
                <p class="text-sm text-gray-600 mt-1">Manage tax rates and application methods</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{{ route('settings.tax.create') }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    ➕ Add Tax Setting
                </a>
                <a href="{{ route('pos.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Back to POS
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    {{ session('error') }}
                </div>
            @endif

            <!-- Current Active Tax -->
            @php $activeTax = $taxSettings->where('is_active', true)->first(); @endphp
            @if($activeTax)
                <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-green-800">🟢 Currently Active Tax Setting</h3>
                            <p class="text-green-700 mt-1">
                                <strong>{{ $activeTax->name }}</strong> - {{ number_format($activeTax->rate_percentage, 2) }}%
                            </p>
                            <p class="text-sm text-green-600 mt-1">
                                Application: 
                                @if($activeTax->isCustomerPays())
                                    <span class="font-medium">Customer Pays Tax</span> (tax added to final price)
                                @else
                                    <span class="font-medium">Business Absorbs Tax</span> (tax included in price)
                                @endif
                            </p>
                        </div>
                        <div class="text-4xl">✅</div>
                    </div>
                </div>
            @else
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
                    <div class="flex items-center">
                        <div class="text-4xl mr-4">⚠️</div>
                        <div>
                            <h3 class="text-lg font-semibold text-yellow-800">No Active Tax Setting</h3>
                            <p class="text-yellow-700">Please activate a tax setting to apply taxes to orders.</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Tax Settings List -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">All Tax Settings</h3>
                    
                    @if($taxSettings->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Application</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($taxSettings as $tax)
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                @if($tax->is_active)
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        ✅ Active
                                                    </span>
                                                @else
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                        ⭕ Inactive
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">{{ $tax->name }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-bold text-blue-600">{{ number_format($tax->rate_percentage, 2) }}%</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                @if($tax->isCustomerPays())
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                        💰 Customer Pays
                                                    </span>
                                                @else
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                        🏢 Business Absorbs
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-gray-500">{{ $tax->description ?: 'No description' }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div class="flex items-center space-x-2">
                                                    @if(!$tax->is_active)
                                                        <form method="POST" action="{{ route('settings.tax.activate', $tax) }}" class="inline">
                                                            @csrf
                                                            <button type="submit" class="text-green-600 hover:text-green-900" title="Activate">
                                                                ✅ Activate
                                                            </button>
                                                        </form>
                                                    @endif
                                                    
                                                    <a href="{{ route('settings.tax.edit', $tax) }}" class="text-indigo-600 hover:text-indigo-900" title="Edit">
                                                        ✏️ Edit
                                                    </a>
                                                    
                                                    @if(!$tax->is_active)
                                                        <form method="POST" action="{{ route('settings.tax.destroy', $tax) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this tax setting?')">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="text-red-600 hover:text-red-900" title="Delete">
                                                                🗑️ Delete
                                                            </button>
                                                        </form>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <div class="text-6xl mb-4">🏷️</div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No Tax Settings</h3>
                            <p class="text-gray-500 mb-4">Create your first tax setting to start applying taxes to orders.</p>
                            <a href="{{ route('settings.tax.create') }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                ➕ Create Tax Setting
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Tax Application Explanation -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-3">💡 Tax Application Methods</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-white p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-700 mb-2">💰 Customer Pays Tax</h4>
                        <p class="text-sm text-gray-600">Tax is added to the final price. Customer sees the tax amount separately on their receipt.</p>
                        <p class="text-xs text-gray-500 mt-1">Example: $10.00 item + $1.00 tax = $11.00 total</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg">
                        <h4 class="font-semibold text-purple-700 mb-2">🏢 Business Absorbs Tax</h4>
                        <p class="text-sm text-gray-600">Tax is included in the displayed price. Business handles tax internally.</p>
                        <p class="text-xs text-gray-500 mt-1">Example: $10.00 item (includes $0.91 tax) = $10.00 total</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
