<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\OrderSetting;

class TaxChargingMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if the tax_charging_method setting already exists
        $existingSetting = OrderSetting::where('key', 'tax_charging_method')->first();
        
        if (!$existingSetting) {
            // Insert the tax charging method setting
            DB::table('order_settings')->insert([
                'key' => 'tax_charging_method',
                'value' => 'customer_pays',
                'type' => 'select',
                'category' => 'tax',
                'label' => 'Tax Charging Method',
                'description' => 'Choose whether customers pay tax separately or business absorbs tax in prices',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            $this->command->info('Tax charging method setting added successfully.');
        } else {
            $this->command->info('Tax charging method setting already exists.');
        }
    }
}