<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Coupon;
use App\Models\TaxSetting;
use App\Models\OrderSetting;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_number',
        'api_user_id',
        'cashier_name',
        'cashier_email',
        'customer_id',
        'table_id',
        'status',
        'isSync',
        'synced_at',
        'sync_error',
        'order_type',
        'party_size',
        'total_amount',
        'tax_amount',
        'service_charge_amount',
        'discount_type',
        'discount_value',
        'discount_amount',
        'coupon_code',
        'points_used',
        'subtotal_amount',
    ];

    protected function casts(): array
    {
        return [
            'total_amount' => 'decimal:2',
            'tax_amount' => 'decimal:2',
            'isSync' => 'boolean',
            'synced_at' => 'datetime',
        ];
    }

    /**
     * Boot the model and set up event listeners
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($order) {
            if (empty($order->transaction_number)) {
                $order->transaction_number = static::generateTransactionNumber();
            }
        });
    }

    /**
     * Generate a unique transaction number
     */
    public static function generateTransactionNumber(): string
    {
        $date = now()->format('Ymd');
        $time = now()->format('His');

        // Get the next sequential number for today
        $lastOrder = static::whereDate('created_at', now()->toDateString())
            ->whereNotNull('transaction_number')
            ->orderBy('id', 'desc')
            ->first();

        $sequence = 1;
        if ($lastOrder && $lastOrder->transaction_number) {
            // Extract sequence number from last transaction number
            $parts = explode('-', $lastOrder->transaction_number);
            if (count($parts) >= 4) {
                $sequence = intval($parts[3]) + 1;
            }
        }

        $sequenceStr = str_pad($sequence, 4, '0', STR_PAD_LEFT);

        return "TXN-{$date}-{$time}-{$sequenceStr}";
    }

    // User relationship removed - using API authentication only

    /**
     * Get the customer for this order.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the table for this order.
     */
    public function table()
    {
        return $this->belongsTo(TableLayout::class, 'table_id');
    }

    /**
     * Check if order has a table assigned.
     */
    public function hasTable(): bool
    {
        return !is_null($this->table_id);
    }

    /**
     * Get table name safely (returns null if no table assigned).
     */
    public function getTableName(): ?string
    {
        return $this->table ? $this->table->name : null;
    }

    /**
     * Get table number safely (returns null if no table assigned).
     */
    public function getTableNumber(): ?string
    {
        return $this->table ? $this->table->table_number : null;
    }

    /**
     * Check if this is a takeout/delivery order (no table assigned).
     */
    public function isTakeoutOrder(): bool
    {
        return is_null($this->table_id);
    }

    /**
     * Get the order items for this order.
     */
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the payments for this order.
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the discounts for this order.
     */
    public function discounts()
    {
        return $this->hasMany(OrderDiscount::class);
    }

    /**
     * Get the order type for this order.
     */
    public function orderType()
    {
        return $this->belongsTo(OrderType::class, 'order_type', 'slug');
    }

    /**
     * Get active discounts for this order.
     */
    public function activeDiscounts()
    {
        return $this->hasMany(OrderDiscount::class)->where('is_active', true);
    }

    /**
     * Calculate the subtotal (before tax).
     */
    public function getSubtotalAttribute()
    {
        return $this->orderItems->sum(function ($item) {
            return $item->quantity * $item->price_at_time;
        });
    }

    /**
     * Calculate and apply discount
     */
    public function applyDiscount($discountType, $discountValue, $couponCode = null, $pointsUsed = 0)
    {
        $subtotal = $this->subtotal;
        $discountAmount = 0;

        switch ($discountType) {
            case 'percentage':
                $discountAmount = ($subtotal * $discountValue) / 100;
                break;
            case 'fixed':
                // Fixed amount discount (e.g. $5 off)
                $discountAmount = min($discountValue, $subtotal); // Can't discount more than subtotal
                break;
            case 'coupon':
                $coupon = Coupon::where('code', $couponCode)->first();
                if ($coupon && $coupon->isValid($subtotal)) {
                    $discountAmount = $coupon->calculateDiscount($subtotal);
                    $discountValue = $coupon->value;
                }
                break;
            case 'points':
                // 1 point = $0.01 discount (100 points = $1)
                $discountAmount = min($pointsUsed * 0.01, $subtotal);
                $discountValue = $pointsUsed;
                break;
            default:
                $discountType = 'none';
                $discountValue = 0;
                $discountAmount = 0;
        }

        // Update order with discount information
        $this->update([
            'discount_type' => $discountType,
            'discount_value' => $discountValue,
            'discount_amount' => $discountAmount,
            'coupon_code' => $couponCode,
            'points_used' => $pointsUsed,
            'subtotal_amount' => $subtotal,
        ]);

        // Recalculate total
        $this->recalculateTotal();

        return $discountAmount;
    }

    /**
     * Recalculate order total with discount and tax
     */
    public function recalculateTotal()
    {
        $subtotal = $this->subtotal_amount ?: $this->subtotal;
        $discountedAmount = $subtotal - $this->discount_amount;
        
        // Get tax settings
        $taxSetting = TaxSetting::getActiveTax();
        $taxRate = OrderSetting::get('tax_rate', 0.10);
        $autoApplyTax = OrderSetting::get('auto_apply_tax', true);
        $taxChargingMethod = OrderSetting::get('tax_charging_method', 'customer_pays');
        
        $taxAmount = 0;
        $total = $discountedAmount;
        
        if ($autoApplyTax && ($taxSetting || $taxRate > 0)) {
            // Use TaxSetting rate if available, otherwise use OrderSetting rate
            $rate = $taxSetting ? $taxSetting->rate : $taxRate;
            
            if ($taxChargingMethod === 'customer_pays') {
                // Customer pays tax - add tax to the total
                $taxAmount = $discountedAmount * $rate;
                $total = $discountedAmount + $taxAmount;
            } else {
                // Business absorbs tax - tax is included in the price, so no additional tax
                $taxAmount = 0;
                $total = $discountedAmount; // Price stays the same
            }
        }
        
        // Add service charge if applicable
        $serviceChargeRate = OrderSetting::get('service_charge_rate', 0.00);
        $autoApplyServiceCharge = OrderSetting::get('auto_apply_service_charge', false);
        $serviceChargeAmount = 0;
        
        if ($autoApplyServiceCharge && $serviceChargeRate > 0) {
            $serviceChargeAmount = $discountedAmount * $serviceChargeRate;
            $total += $serviceChargeAmount;
        }

        $this->update([
            'tax_amount' => $taxAmount,
            'service_charge_amount' => $serviceChargeAmount,
            'total_amount' => $total,
        ]);
    }

    /**
     * Check if order is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'Pending';
    }

    /**
     * Check if order is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'Completed';
    }

    /**
     * Check if order is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'Cancelled';
    }

    /**
     * Check if order is paid.
     */
    public function isPaid(): bool
    {
        return $this->status === 'Paid';
    }

    /**
     * Add a discount to this order
     */
    public function addDiscount($type, $value, $couponCode = null, $pointsUsed = null, $description = null)
    {
        // Calculate discount amount based on current subtotal
        $subtotal = $this->getSubtotalAmount();
        $discountAmount = 0;

        switch ($type) {
            case 'percentage':
                $discountAmount = $subtotal * ($value / 100);
                break;
            case 'fixed':
                $discountAmount = min($value, $subtotal); // Don't exceed subtotal
                break;
            case 'points':
                $discountAmount = min($pointsUsed / 100, $subtotal); // 100 points = $1
                break;
            case 'coupon':
                // Coupon amount should be passed in $value
                $discountAmount = min($value, $subtotal);
                break;
        }

        // Create the discount record
        return $this->discounts()->create([
            'type' => $type,
            'value' => $value,
            'amount' => $discountAmount,
            'coupon_code' => $couponCode,
            'points_used' => $pointsUsed,
            'description' => $description,
            'is_active' => true,
        ]);
    }

    /**
     * Remove a specific discount
     */
    public function removeDiscount($discountId)
    {
        return $this->discounts()->where('id', $discountId)->delete();
    }

    /**
     * Get total discount amount from all active discounts
     */
    public function getTotalDiscountAmount()
    {
        return $this->activeDiscounts()->sum('amount');
    }

    /**
     * Get subtotal amount (before discounts, tax, service charge)
     */
    public function getSubtotalAmount()
    {
        return $this->orderItems->sum(function ($item) {
            return $item->quantity * $item->price_at_time;
        });
    }

    /**
     * Recalculate all discount amounts based on current subtotal
     */
    public function recalculateDiscounts()
    {
        $subtotal = $this->getSubtotalAmount();

        foreach ($this->activeDiscounts as $discount) {
            $newAmount = 0;

            switch ($discount->type) {
                case 'percentage':
                    $newAmount = $subtotal * ($discount->value / 100);
                    break;
                case 'fixed':
                    $newAmount = min($discount->value, $subtotal);
                    break;
                case 'points':
                    $newAmount = min($discount->points_used / 100, $subtotal);
                    break;
                case 'coupon':
                    $newAmount = min($discount->value, $subtotal);
                    break;
            }

            $discount->update(['amount' => $newAmount]);
        }
    }

    /**
     * Mark order as synced to API
     */
    public function markAsSynced(): void
    {
        $this->update([
            'isSync' => true,
            'synced_at' => now(),
            'sync_error' => null
        ]);
    }

    /**
     * Mark order as sync failed with error message
     */
    public function markSyncFailed(string $error): void
    {
        $this->update([
            'isSync' => false,
            'synced_at' => null,
            'sync_error' => $error
        ]);
    }

    /**
     * Check if order needs to be synced
     */
    public function needsSync(): bool
    {
        return !$this->isSync && in_array($this->status, ['Paid', 'Cancelled']);
    }

    /**
     * Get sync status display
     */
    public function getSyncStatusAttribute(): string
    {
        if ($this->isSync) {
            return 'Synced';
        }

        if ($this->sync_error) {
            return 'Failed';
        }

        if ($this->needsSync()) {
            return 'Pending';
        }

        return 'Not Required';
    }

    /**
     * Scope for unsynced orders
     */
    public function scopeUnsynced($query)
    {
        return $query->where('isSync', false)
                    ->whereIn('status', ['Paid', 'Cancelled']);
    }

    /**
     * Scope for sync failed orders
     */
    public function scopeSyncFailed($query)
    {
        return $query->where('isSync', false)
                    ->whereNotNull('sync_error');
    }
}
