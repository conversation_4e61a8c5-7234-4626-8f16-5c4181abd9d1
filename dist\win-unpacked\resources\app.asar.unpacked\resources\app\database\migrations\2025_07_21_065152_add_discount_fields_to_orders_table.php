<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('discount_type', ['none', 'percentage', 'fixed', 'coupon', 'points'])->default('none')->after('tax_amount');
            $table->decimal('discount_value', 10, 2)->default(0)->after('discount_type');
            $table->decimal('discount_amount', 10, 2)->default(0)->after('discount_value');
            $table->string('coupon_code')->nullable()->after('discount_amount');
            $table->integer('points_used')->default(0)->after('coupon_code');
            $table->decimal('subtotal_amount', 10, 2)->default(0)->after('points_used');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn([
                'discount_type',
                'discount_value',
                'discount_amount',
                'coupon_code',
                'points_used',
                'subtotal_amount'
            ]);
        });
    }
};
