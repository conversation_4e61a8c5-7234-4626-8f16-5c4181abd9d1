/**
 * Network Connectivity Monitor for POS System
 * Monitors internet connectivity and API endpoint availability
 */

class NetworkMonitor {
    constructor() {
        this.status = 'unknown'; // Start with unknown instead of checking
        this.lastStatus = 'unknown'; // Store last known status
        this.lastCheck = null;
        this.checkInterval = null;
        this.apiEndpoint = '/api/health-check'; // We'll create this endpoint
        this.fallbackEndpoints = [
            'https://www.google.com/favicon.ico',
            'https://httpbin.org/status/200',
            '/debug-user-role' // Local endpoint as fallback
        ];
        this.callbacks = [];
        this.connectionSpeed = null;
        this.latency = null;
        this.isChecking = false; // Track if currently checking

        this.init();
    }

    init() {
        this.createIndicatorElement();
        this.bindEvents();
        this.startMonitoring();
        this.checkInitialConnection();
    }

    createIndicatorElement() {
        // Create the network indicator HTML with signal bars only
        const indicatorHTML = `
            <div id="signal-bars" class="flex items-end space-x-0.5 h-4 w-5 mr-2">
                <div class="signal-bar bg-gray-300 w-1 h-1 rounded-sm transition-all duration-300"></div>
                <div class="signal-bar bg-gray-300 w-1 h-2 rounded-sm transition-all duration-300"></div>
                <div class="signal-bar bg-gray-300 w-1 h-3 rounded-sm transition-all duration-300"></div>
                <div class="signal-bar bg-gray-300 w-1 h-4 rounded-sm transition-all duration-300"></div>
            </div>

            <!-- Tooltip -->
            <div id="network-tooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                <div id="tooltip-content">Click to refresh</div>
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
            </div>
        `;

        // Find the dropdown trigger button and add the indicator inside it, before the user name
        const dropdownButton = document.querySelector('nav .hidden.sm\\:flex button');
        if (dropdownButton) {
            // Add relative positioning and group class for tooltip
            dropdownButton.classList.add('relative', 'group');

            // Create indicator container
            const indicatorContainer = document.createElement('div');
            indicatorContainer.id = 'network-indicator';
            indicatorContainer.className = 'flex items-center cursor-pointer';
            indicatorContainer.innerHTML = indicatorHTML;

            // Insert before the first div (which contains the user name)
            const userNameDiv = dropdownButton.querySelector('div');
            if (userNameDiv) {
                dropdownButton.insertBefore(indicatorContainer, userNameDiv);
            }
        }
    }

    bindEvents() {
        // Listen for online/offline events
        window.addEventListener('online', () => this.handleConnectionChange(true));
        window.addEventListener('offline', () => this.handleConnectionChange(false));
        
        // Listen for visibility change to check when tab becomes active
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.checkConnection();
            }
        });

        // Click handler for manual refresh
        const indicator = document.getElementById('network-indicator');
        if (indicator) {
            indicator.addEventListener('click', () => {
                this.checkConnection(true);
            });
            indicator.style.cursor = 'pointer';
            indicator.title = 'Click to refresh connection status';
        }
    }

    startMonitoring() {
        // Check connection every 30 seconds
        this.checkInterval = setInterval(() => {
            this.checkConnection();
        }, 30000);
    }

    stopMonitoring() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
    }

    async checkInitialConnection() {
        await this.checkConnection();
    }

    async checkConnection(forceCheck = false) {
        const now = Date.now();

        // Avoid too frequent checks unless forced
        if (!forceCheck && this.lastCheck && (now - this.lastCheck) < 5000) {
            return;
        }

        // If already checking, don't start another check
        if (this.isChecking && !forceCheck) {
            return;
        }

        this.isChecking = true;
        this.lastCheck = now;

        // Store current status as last known status before checking
        if (this.status !== 'checking' && this.status !== 'unknown') {
            this.lastStatus = this.status;
        }

        // Show checking state with subtle animation but keep last status info
        // this.showCheckingState();

        try {
            const startTime = performance.now();
            
            // First, check our API endpoint
            const apiResult = await this.checkApiEndpoint();
            
            if (apiResult.success) {
                const endTime = performance.now();
                this.latency = Math.round(endTime - startTime);
                this.status = 'connected';
                this.updateIndicator('connected');
                this.notifyCallbacks('connected', { latency: this.latency });
            } else {
                // If API fails, check fallback endpoints
                const fallbackResult = await this.checkFallbackEndpoints();

                if (fallbackResult.success) {
                    this.status = 'unstable';
                    this.updateIndicator('unstable');
                    this.notifyCallbacks('unstable', { reason: 'api_unavailable' });
                } else {
                    this.status = 'disconnected';
                    this.updateIndicator('disconnected');
                    this.notifyCallbacks('disconnected', { reason: 'no_internet' });
                }
            }

        } catch (error) {
            console.error('Network check failed:', error);
            this.status = 'disconnected';
            this.updateIndicator('disconnected');
            this.notifyCallbacks('disconnected', { reason: 'check_failed', error });
        } finally {
            this.isChecking = false;
        }
    }

    async checkApiEndpoint() {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000);

            const response = await fetch(this.apiEndpoint, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                },
                signal: controller.signal,
                cache: 'no-cache'
            });

            clearTimeout(timeoutId);
            return { success: response.ok, status: response.status };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async checkFallbackEndpoints() {
        for (const endpoint of this.fallbackEndpoints) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 8000);

                const response = await fetch(endpoint, {
                    method: 'GET',
                    mode: endpoint.startsWith('http') ? 'no-cors' : 'cors',
                    signal: controller.signal,
                    cache: 'no-cache'
                });

                clearTimeout(timeoutId);
                if (response.ok || response.type === 'opaque') {
                    return { success: true, endpoint };
                }
            } catch (error) {
                continue; // Try next endpoint
            }
        }
        return { success: false };
    }

    showCheckingState() {
        const signalBars = document.querySelectorAll('#signal-bars .signal-bar');
        const indicator = document.getElementById('network-indicator');

        if (!signalBars.length || !indicator) return;

        // Add subtle pulse animation to signal bars during checking
        // signalBars.forEach(bar => {
        //     bar.classList.add('animate-pulse');
        // });

        // Keep the last known status styling but add checking indicator
        indicator.title = 'Checking connection...';
    }

    updateIndicator(status) {
        const indicator = document.getElementById('network-indicator');
        const signalBars = document.querySelectorAll('#signal-bars .signal-bar');

        if (!indicator || !signalBars.length) return;

        // Remove all status classes and animations
        indicator.className = indicator.className.replace(/bg-\w+-\d+/g, '').replace(/text-\w+-\d+/g, '');
        signalBars.forEach(bar => {
            bar.className = bar.className.replace(/bg-\w+-\d+/g, '');
            bar.classList.remove('animate-pulse');
        });

        // Apply status-specific styles and signal bar configuration
        let tooltipText = 'Network Status';
        let barColor = 'bg-gray-400';
        let activeBars = 0;

        switch (status) {
            case 'connected':
                barColor = 'bg-green-500';
                activeBars = this.latency < 100 ? 4 : this.latency < 300 ? 3 : 2;
                tooltipText = `Connected (${this.latency}ms)`;
                break;
            case 'unstable':
                barColor = 'bg-yellow-500';
                activeBars = 2;
                tooltipText = 'Unstable Connection';
                break;
            case 'disconnected':
                barColor = 'bg-red-500';
                activeBars = 0;
                tooltipText = 'No Connection';
                break;
            default: // unknown status
                barColor = 'bg-gray-400';
                activeBars = 1;
                tooltipText = 'Unknown Status';
                break;
        }

        // Update signal bars
        signalBars.forEach((bar, index) => {
            if (index < activeBars) {
                bar.classList.add(barColor);
                bar.classList.remove('bg-gray-300');
            } else {
                bar.classList.add('bg-gray-300');
                bar.classList.remove(barColor);
            }
        });

        // Update tooltip
        indicator.title = tooltipText;

        // Add visual feedback for POS operations
        this.updatePosWarnings(status);
    }

    updatePosWarnings(status) {
        // Show warnings for POS-critical operations when connection is poor
        if (status === 'disconnected' || status === 'unstable') {
            this.showConnectionWarning(status);
        } else {
            this.hideConnectionWarning();
        }
    }

    showConnectionWarning(status) {
        // Remove existing warning
        this.hideConnectionWarning();

        const warningMessage = status === 'disconnected'
            ? 'No internet connection. POS operations may fail.'
            : 'Unstable connection. Some POS features may be slow.';

        const warning = document.createElement('div');
        warning.id = 'network-warning';
        warning.className = `fixed top-20 right-4 bg-${status === 'disconnected' ? 'red' : 'yellow'}-100 border border-${status === 'disconnected' ? 'red' : 'yellow'}-300 text-${status === 'disconnected' ? 'red' : 'yellow'}-800 px-4 py-2 rounded-md shadow-lg z-50 text-sm max-w-sm`;
        warning.innerHTML = `
            <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-${status === 'disconnected' ? 'red' : 'yellow'}-500 rounded-full"></div>
                <span>${warningMessage}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-${status === 'disconnected' ? 'red' : 'yellow'}-600 hover:text-${status === 'disconnected' ? 'red' : 'yellow'}-800">×</button>
            </div>
        `;

        document.body.appendChild(warning);

        // Auto-hide after 10 seconds
        setTimeout(() => {
            if (document.getElementById('network-warning')) {
                warning.remove();
            }
        }, 10000);
    }

    hideConnectionWarning() {
        const existing = document.getElementById('network-warning');
        if (existing) {
            existing.remove();
        }
    }

    handleConnectionChange(isOnline) {
        if (isOnline) {
            // Delay check to allow connection to stabilize
            setTimeout(() => this.checkConnection(true), 1000);
        } else {
            this.status = 'disconnected';
            this.updateIndicator('disconnected');
            this.notifyCallbacks('disconnected', { reason: 'browser_offline' });
        }
    }

    // Public API for other components to listen to network changes
    onStatusChange(callback) {
        this.callbacks.push(callback);
    }

    notifyCallbacks(status, details) {
        this.callbacks.forEach(callback => {
            try {
                callback(status, details);
            } catch (error) {
                console.error('Network monitor callback error:', error);
            }
        });
    }

    getStatus() {
        return {
            status: this.status,
            latency: this.latency,
            lastCheck: this.lastCheck
        };
    }

    destroy() {
        this.stopMonitoring();
        const indicator = document.getElementById('network-indicator');
        if (indicator) {
            indicator.remove();
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.networkMonitor = new NetworkMonitor();
});

// Export for module usage
export default NetworkMonitor;
