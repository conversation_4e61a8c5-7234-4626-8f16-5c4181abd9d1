<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_discounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->string('type'); // percentage, fixed, coupon, points
            $table->decimal('value', 10, 2); // percentage rate or fixed amount
            $table->decimal('amount', 10, 2); // calculated discount amount
            $table->string('coupon_code')->nullable(); // for coupon discounts
            $table->integer('points_used')->nullable(); // for points discounts
            $table->string('description')->nullable(); // human-readable description
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['order_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_discounts');
    }
};
