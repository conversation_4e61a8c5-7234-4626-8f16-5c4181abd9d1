<?php

namespace App\Guards;

use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Http\Request;

class ApiSessionGuard implements Guard
{
    protected $request;
    protected $user;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Determine if the current user is authenticated.
     */
    public function check(): bool
    {
        return $this->request->session()->get('api_authenticated', false);
    }

    /**
     * Determine if the current user is a guest.
     */
    public function guest(): bool
    {
        return !$this->check();
    }

    /**
     * Get the currently authenticated user.
     */
    public function user(): ?Authenticatable
    {
        if ($this->user) {
            return $this->user;
        }

        if ($this->check()) {
            $apiUser = $this->request->session()->get('api_user');
            if ($apiUser) {
                $this->user = new ApiUser($apiUser);
                return $this->user;
            }
        }

        return null;
    }

    /**
     * Get the ID for the currently authenticated user.
     */
    public function id()
    {
        $user = $this->user();
        return $user ? $user->getAuthIdentifier() : null;
    }

    /**
     * Determine if the guard has a user instance.
     */
    public function hasUser(): bool
    {
        return !is_null($this->user);
    }

    /**
     * Validate a user's credentials.
     */
    public function validate(array $credentials = []): bool
    {
        // This is handled by the LoginRequest, not the guard
        return false;
    }

    /**
     * Attempt to authenticate a user using the given credentials.
     */
    public function attempt(array $credentials = [], $remember = false): bool
    {
        // This is handled by the LoginRequest, not the guard
        return false;
    }

    /**
     * Log a user into the application without sessions or cookies.
     */
    public function once(array $credentials = []): bool
    {
        return false;
    }

    /**
     * Log the given user ID into the application.
     */
    public function loginUsingId($id, $remember = false): ?Authenticatable
    {
        return null;
    }

    /**
     * Log the given user ID into the application without sessions or cookies.
     */
    public function onceUsingId($id): ?Authenticatable
    {
        return null;
    }

    /**
     * Determine if the user was authenticated via "remember me" cookie.
     */
    public function viaRemember(): bool
    {
        return false;
    }

    /**
     * Log the user out of the application.
     */
    public function logout(): void
    {
        $this->request->session()->forget([
            'api_token',
            'api_user',
            'api_authenticated',
            'api_outlet_id',
            'api_outlet_category',
            'pos_needs_tables'
        ]);
        $this->user = null;
    }

    /**
     * Set the current user.
     */
    public function setUser(Authenticatable $user): void
    {
        $this->user = $user;
    }
}


