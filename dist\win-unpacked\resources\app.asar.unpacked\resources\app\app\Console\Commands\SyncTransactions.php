<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TransactionSyncService;
use App\Models\Order;

class SyncTransactions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:sync-transactions
                            {--force : Force sync even if network check fails}
                            {--order= : Sync specific order by ID}
                            {--stats : Show sync statistics only}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync unsynced transactions to the external API';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $transactionSyncService = app(TransactionSyncService::class);

        // Show stats only
        if ($this->option('stats')) {
            $this->showSyncStats($transactionSyncService);
            return 0;
        }

        // Sync specific order
        if ($orderId = $this->option('order')) {
            return $this->syncSpecificOrder($orderId, $transactionSyncService);
        }

        // Sync all unsynced transactions
        return $this->syncAllTransactions($transactionSyncService);
    }

    /**
     * Show sync statistics
     */
    private function showSyncStats(TransactionSyncService $service): void
    {
        $stats = $service->getSyncStats();

        $this->info('📊 Transaction Sync Statistics');
        $this->line('');
        $this->line("Total Syncable Orders: {$stats['total_syncable_orders']}");
        $this->line("Synced Transactions: {$stats['synced_transactions']}");
        $this->line("Unsynced Transactions: {$stats['unsynced_transactions']}");
        $this->line("Failed Transactions: {$stats['failed_transactions']}");
        $this->line("Last Sync: " . ($stats['last_sync'] ? $stats['last_sync'] : 'Never'));
    }

    /**
     * Sync specific order
     */
    private function syncSpecificOrder(int $orderId, TransactionSyncService $service): int
    {
        $order = Order::find($orderId);

        if (!$order) {
            $this->error("Order with ID {$orderId} not found.");
            return 1;
        }

        $this->info("Syncing order {$orderId} (Transaction: {$order->transaction_number})...");

        $result = $service->syncTransaction($order);

        if ($result['success']) {
            $this->info("✅ Successfully synced order {$orderId}");
            return 0;
        } else {
            $this->error("❌ Failed to sync order {$orderId}: {$result['error']}");
            return 1;
        }
    }

    /**
     * Sync all unsynced transactions
     */
    private function syncAllTransactions(TransactionSyncService $service): int
    {
        $this->info('🔄 Starting background transaction sync...');

        // Get unsynced count first
        $unsyncedCount = Order::whereIn('status', ['Paid', 'Cancelled'])
            ->where('isSync', false)
            ->count();

        if ($unsyncedCount === 0) {
            $this->info('✅ No transactions to sync.');
            return 0;
        }

        $this->info("Found {$unsyncedCount} unsynced transactions.");

        $result = $service->syncAllUnsyncedTransactions();

        if ($result['success']) {
            $this->info("✅ {$result['message']}");
            return 0;
        } else {
            $this->error("❌ {$result['message']}");

            // Show errors if any
            if (!empty($result['stats']['errors'])) {
                $this->line('');
                $this->error('Errors encountered:');
                foreach ($result['stats']['errors'] as $error) {
                    $this->line("  - {$error}");
                }
            }

            return 1;
        }
    }
}
