@tailwind base;
@tailwind components;
@tailwind utilities;

/* Network Monitor Styles */
@layer components {
    #network-indicator {
        @apply transition-all duration-300 ease-in-out;
    }

    #network-indicator:hover {
        @apply scale-105;
    }

    /* Signal bars container */
    #signal-bars {
        @apply flex items-end space-x-0.5;
    }

    /* Individual signal bar */
    .signal-bar {
        @apply transition-all duration-300 ease-in-out rounded-sm;
        min-width: 3px;
    }

    /* Signal bar hover effect - now triggered by button hover */
    button:hover #network-indicator .signal-bar {
        @apply transform scale-110;
    }

    /* Pulse animation for checking state */
    @keyframes signal-pulse {
        0%, 100% {
            opacity: 1;
            transform: scaleY(1);
        }
        50% {
            opacity: 0.6;
            transform: scaleY(0.8);
        }
    }

    .signal-bar.animate-pulse {
        animation: signal-pulse 1.5s ease-in-out infinite;
    }

    /* Status-specific styles */
    .network-connected {
        @apply bg-green-50 text-green-700 border border-green-200;
    }

    .network-unstable {
        @apply bg-yellow-50 text-yellow-700 border border-yellow-200;
    }

    .network-disconnected {
        @apply bg-red-50 text-red-700 border border-red-200;
    }

    .network-unknown {
        @apply bg-gray-50 text-gray-600 border border-gray-200;
    }

    .network-checking {
        @apply bg-gray-50 text-gray-600 border border-gray-200;
    }
}

