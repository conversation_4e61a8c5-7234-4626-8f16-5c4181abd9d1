<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Drop the existing foreign key constraint
            $table->dropForeign(['table_id']);
            
            // Modify the column to be nullable
            $table->foreignId('table_id')->nullable()->change();
            
            // Re-add the foreign key constraint with nullable support
            $table->foreign('table_id')->references('id')->on('table_layouts')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Drop the nullable foreign key constraint
            $table->dropForeign(['table_id']);
            
            // Modify the column back to not nullable
            $table->foreignId('table_id')->nullable(false)->change();
            
            // Re-add the original foreign key constraint
            $table->foreign('table_id')->references('id')->on('table_layouts')->onDelete('cascade');
        });
    }
};
