<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Order;
use App\Models\Customer;
use App\Models\Payment;
use App\Models\OrderItem;
use App\Models\MenuItem;
use Carbon\Carbon;

class AnalyticsDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Only seed if there are no existing orders
        if (Order::count() > 0) {
            $this->command->info('Orders already exist. Skipping analytics data seeding.');
            return;
        }

        $this->command->info('Creating sample analytics data...');

        // Get existing customers and menu items
        $customers = Customer::all();
        $menuItems = MenuItem::all();

        if ($customers->isEmpty() || $menuItems->isEmpty()) {
            $this->command->warn('No customers or menu items found. Please seed customers and menu items first.');
            return;
        }

        // Create sample orders for the last 30 days
        for ($i = 0; $i < 30; $i++) {
            $date = Carbon::now()->subDays($i);
            
            // Create 3-8 orders per day
            $ordersPerDay = rand(3, 8);
            
            for ($j = 0; $j < $ordersPerDay; $j++) {
                $customer = $customers->random();
                $orderTime = $date->copy()->addHours(rand(8, 22))->addMinutes(rand(0, 59));
                
                // Create order
                $order = Order::create([
                    'api_user_id' => 1,
                    'cashier_name' => 'Sample Cashier',
                    'cashier_email' => '<EMAIL>',
                    'customer_id' => $customer->id,
                    'table_id' => rand(1, 10),
                    'status' => 'Completed',
                    'order_type' => rand(0, 1) ? 'dine_in' : 'take_away',
                    'party_size' => rand(1, 6),
                    'total_amount' => 0, // Will be calculated
                    'tax_amount' => 0,
                    'service_charge_amount' => 0,
                    'discount_amount' => 0,
                    'subtotal_amount' => 0,
                    'isSync' => rand(0, 1) ? true : false,
                    'synced_at' => rand(0, 1) ? $orderTime->copy()->addMinutes(rand(5, 30)) : null,
                    'created_at' => $orderTime,
                    'updated_at' => $orderTime,
                ]);

                // Add 1-5 items to each order
                $itemCount = rand(1, 5);
                $subtotal = 0;
                
                for ($k = 0; $k < $itemCount; $k++) {
                    $menuItem = $menuItems->random();
                    $quantity = rand(1, 3);
                    $price = $menuItem->price;
                    $itemTotal = $quantity * $price;
                    $subtotal += $itemTotal;
                    
                    OrderItem::create([
                        'order_id' => $order->id,
                        'menu_item_id' => $menuItem->id,
                        'quantity' => $quantity,
                        'price_at_time' => $price,
                        'notes' => rand(0, 1) ? 'Sample note' : null,
                    ]);
                }

                // Calculate totals
                $taxRate = 0.1; // 10% tax
                $taxAmount = $subtotal * $taxRate;
                $discountAmount = rand(0, 1) ? rand(5, 20) : 0; // Random discount
                $totalAmount = $subtotal + $taxAmount - $discountAmount;

                // Update order totals
                $order->update([
                    'subtotal_amount' => $subtotal,
                    'tax_amount' => $taxAmount,
                    'discount_amount' => $discountAmount,
                    'total_amount' => $totalAmount,
                ]);

                // Create payment
                $paymentMethods = ['cash', 'card', 'digital_wallet'];
                Payment::create([
                    'order_id' => $order->id,
                    'method' => $paymentMethods[array_rand($paymentMethods)],
                    'amount_paid' => $totalAmount,
                    'created_at' => $orderTime->copy()->addMinutes(rand(1, 10)),
                    'updated_at' => $orderTime->copy()->addMinutes(rand(1, 10)),
                ]);

                // Randomly mark some orders as sync failed
                if (!$order->isSync && rand(0, 1)) {
                    $order->update([
                        'sync_error' => 'Sample sync error: Connection timeout'
                    ]);
                }
            }
        }

        $this->command->info('Sample analytics data created successfully!');
        $this->command->info('Created ' . Order::count() . ' orders with payments and items.');
    }
}
