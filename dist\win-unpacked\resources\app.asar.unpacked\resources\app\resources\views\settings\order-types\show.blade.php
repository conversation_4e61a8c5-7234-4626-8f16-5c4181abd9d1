<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Order Type Details: ') }}{{ $orderType->name }}
            </h2>
            <div class="space-x-2">
                <a href="{{ route('settings.order-types.edit', $orderType) }}" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                    Edit
                </a>
                <a href="{{ route('settings.order-types.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Back to Order Types
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Order Type Details: {{ $orderType->name }}</h3>
                        <div class="space-x-2">
                            <a href="{{ route('settings.order-types.edit', $orderType) }}" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a href="{{ route('settings.order-types.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                <i class="fas fa-arrow-left"></i> Back to Order Types
                            </a>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                            <table class="min-w-full">
                                <tr class="border-b border-gray-200">
                                    <th class="py-3 pr-6 text-left text-sm font-medium text-gray-900 w-48">Name:</th>
                                    <td class="py-3 text-sm text-gray-900">
                                        <div class="flex items-center">
                                            @if($orderType->icon)
                                                <i class="{{ $orderType->icon }} mr-2" 
                                                   @if($orderType->color) style="color: {{ $orderType->color }}" @endif></i>
                                            @endif
                                            <span class="font-medium">{{ $orderType->name }}</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="border-b border-gray-200">
                                    <th class="py-3 pr-6 text-left text-sm font-medium text-gray-900">Slug:</th>
                                    <td class="py-3 text-sm text-gray-900"><code class="bg-gray-100 px-2 py-1 rounded">{{ $orderType->slug }}</code></td>
                                </tr>
                                <tr class="border-b border-gray-200">
                                    <th class="py-3 pr-6 text-left text-sm font-medium text-gray-900">Description:</th>
                                    <td class="py-3 text-sm text-gray-900">{{ $orderType->description ?: 'No description provided' }}</td>
                                </tr>
                                <tr class="border-b border-gray-200">
                                    <th class="py-3 pr-6 text-left text-sm font-medium text-gray-900">Sort Order:</th>
                                    <td class="py-3 text-sm text-gray-900">{{ $orderType->sort_order }}</td>
                                </tr>
                                <tr class="border-b border-gray-200">
                                    <th class="py-3 pr-6 text-left text-sm font-medium text-gray-900">Status:</th>
                                    <td class="py-3 text-sm text-gray-900">
                                        @if($orderType->is_active)
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                                        @else
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Inactive</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                        
                        <div>
                            <table class="min-w-full">
                                <tr class="border-b border-gray-200">
                                    <th class="py-3 pr-6 text-left text-sm font-medium text-gray-900 w-48">Requires Table:</th>
                                    <td class="py-3 text-sm text-gray-900">
                                        @if($orderType->requires_table)
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"><i class="fas fa-check mr-1"></i> Yes</span>
                                        @else
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"><i class="fas fa-times mr-1"></i> No</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr class="border-b border-gray-200">
                                    <th class="py-3 pr-6 text-left text-sm font-medium text-gray-900">Requires Party Size:</th>
                                    <td class="py-3 text-sm text-gray-900">
                                        @if($orderType->requires_party_size)
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"><i class="fas fa-check mr-1"></i> Yes</span>
                                        @else
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"><i class="fas fa-times mr-1"></i> No</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr class="border-b border-gray-200">
                                    <th class="py-3 pr-6 text-left text-sm font-medium text-gray-900">Icon:</th>
                                    <td class="py-3 text-sm text-gray-900">
                                        @if($orderType->icon)
                                            <i class="{{ $orderType->icon }}" 
                                               @if($orderType->color) style="color: {{ $orderType->color }}" @endif></i>
                                            <code class="ml-2 bg-gray-100 px-2 py-1 rounded">{{ $orderType->icon }}</code>
                                        @else
                                            <span class="text-gray-500">No icon set</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr class="border-b border-gray-200">
                                    <th class="py-3 pr-6 text-left text-sm font-medium text-gray-900">Color:</th>
                                    <td class="py-3 text-sm text-gray-900">
                                        @if($orderType->color)
                                            <span class="inline-block rounded" 
                                                  style="width: 20px; height: 20px; background-color: {{ $orderType->color }}; border: 1px solid #ddd;"></span>
                                            <code class="ml-2 bg-gray-100 px-2 py-1 rounded">{{ $orderType->color }}</code>
                                        @else
                                            <span class="text-gray-500">No color set</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr class="border-b border-gray-200">
                                    <th class="py-3 pr-6 text-left text-sm font-medium text-gray-900">Created:</th>
                                    <td class="py-3 text-sm text-gray-900">{{ $orderType->created_at->format('M d, Y H:i') }}</td>
                                </tr>
                                <tr class="border-b border-gray-200">
                                    <th class="py-3 pr-6 text-left text-sm font-medium text-gray-900">Last Updated:</th>
                                    <td class="py-3 text-sm text-gray-900">{{ $orderType->updated_at->format('M d, Y H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($orderType->orders()->exists())
                        <div class="mt-8">
                            <h5 class="text-lg font-medium text-gray-900 mb-4">Usage Statistics</h5>
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div class="bg-blue-500 text-white rounded-lg p-4 text-center">
                                    <h4 class="text-2xl font-bold">{{ $orderType->orders()->count() }}</h4>
                                    <p class="text-sm">Total Orders</p>
                                </div>
                                <div class="bg-green-500 text-white rounded-lg p-4 text-center">
                                    <h4 class="text-2xl font-bold">{{ $orderType->orders()->where('status', 'completed')->count() }}</h4>
                                    <p class="text-sm">Completed Orders</p>
                                </div>
                                <div class="bg-yellow-500 text-white rounded-lg p-4 text-center">
                                    <h4 class="text-2xl font-bold">{{ $orderType->orders()->whereIn('status', ['pending', 'preparing'])->count() }}</h4>
                                    <p class="text-sm">Active Orders</p>
                                </div>
                                <div class="bg-indigo-500 text-white rounded-lg p-4 text-center">
                                    <h4 class="text-2xl font-bold">${{ number_format($orderType->orders()->where('status', 'completed')->sum('total_amount'), 2) }}</h4>
                                    <p class="text-sm">Total Revenue</p>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="mt-8">
                            <div class="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded">
                                <i class="fas fa-info-circle mr-2"></i>
                                This order type has not been used in any orders yet.
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
        </div>
    </div>
</x-app-layout>