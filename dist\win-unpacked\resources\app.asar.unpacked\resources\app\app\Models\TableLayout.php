<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class TableLayout extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'status',
    ];

    /**
     * Get the orders for this table.
     */
    public function orders()
    {
        return $this->hasMany(Order::class, 'table_id');
    }

    /**
     * Get the current active order for this table.
     */
    public function currentOrder()
    {
        return $this->hasOne(Order::class, 'table_id')
                    ->whereIn('status', ['Pending', 'In Progress', 'Preparing', 'Ready'])
                    ->latest();
    }

    /**
     * Get the most recent order for this table (including completed/cancelled).
     */
    public function latestOrder()
    {
        return $this->hasOne(Order::class, 'table_id')->latest();
    }

    /**
     * Check if table has any active orders.
     */
    public function hasActiveOrder(): bool
    {
        return $this->orders()
                    ->whereIn('status', ['Pending', 'In Progress', 'Preparing', 'Ready'])
                    ->exists();
    }

    /**
     * Check if table is available.
     */
    public function isAvailable(): bool
    {
        return $this->status === 'Available' && !$this->hasActiveOrder();
    }

    /**
     * Check if table is occupied.
     */
    public function isOccupied(): bool
    {
        return $this->status === 'Occupied' || $this->hasActiveOrder();
    }
}
