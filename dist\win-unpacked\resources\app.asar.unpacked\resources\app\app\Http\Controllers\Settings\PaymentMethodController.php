<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PaymentMethodController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $paymentMethods = PaymentMethod::ordered()->get();
        return view('settings.payment-methods.index', compact('paymentMethods'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('settings.payment-methods.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:payment_methods,name',
            'description' => 'nullable|string|max:500',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'requires_reference' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->name);
        
        // Set default sort order if not provided
        if (!isset($data['sort_order'])) {
            $data['sort_order'] = PaymentMethod::max('sort_order') + 1;
        }

        // Convert checkboxes to boolean
        $data['requires_reference'] = $request->has('requires_reference');
        $data['is_active'] = $request->has('is_active');

        PaymentMethod::create($data);

        return redirect()->route('settings.payment-methods.index')
            ->with('success', 'Payment method created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(PaymentMethod $paymentMethod)
    {
        return view('settings.payment-methods.show', compact('paymentMethod'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PaymentMethod $paymentMethod)
    {
        return view('settings.payment-methods.edit', compact('paymentMethod'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PaymentMethod $paymentMethod)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:payment_methods,name,' . $paymentMethod->id,
            'description' => 'nullable|string|max:500',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'requires_reference' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->name);

        // Convert checkboxes to boolean
        $data['requires_reference'] = $request->has('requires_reference');
        $data['is_active'] = $request->has('is_active');

        $paymentMethod->update($data);

        return redirect()->route('settings.payment-methods.index')
            ->with('success', 'Payment method updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PaymentMethod $paymentMethod)
    {
        // Check if payment method is being used by any payments
        if ($paymentMethod->payments()->exists()) {
            return redirect()->route('settings.payment-methods.index')
                ->with('error', 'Cannot delete payment method that is being used by existing payments.');
        }

        $paymentMethod->delete();

        return redirect()->route('settings.payment-methods.index')
            ->with('success', 'Payment method deleted successfully.');
    }

    /**
     * Update the sort order of payment methods
     */
    public function updateOrder(Request $request)
    {
        $request->validate([
            'payment_methods' => 'required|array',
            'payment_methods.*' => 'exists:payment_methods,id'
        ]);

        foreach ($request->payment_methods as $index => $paymentMethodId) {
            PaymentMethod::where('id', $paymentMethodId)->update(['sort_order' => $index + 1]);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Toggle the active status of a payment method
     */
    public function toggleStatus(PaymentMethod $paymentMethod)
    {
        $paymentMethod->update(['is_active' => !$paymentMethod->is_active]);

        return redirect()->route('settings.payment-methods.index')
            ->with('success', 'Payment method status updated successfully.');
    }
}
