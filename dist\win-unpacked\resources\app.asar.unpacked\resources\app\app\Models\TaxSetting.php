<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TaxSetting extends Model
{
    protected $fillable = [
        'name',
        'rate',
        'is_active',
        'application_type',
        'description'
    ];

    protected $casts = [
        'rate' => 'decimal:4',
        'is_active' => 'boolean'
    ];

    /**
     * Get the active tax setting
     */
    public static function getActiveTax()
    {
        return self::where('is_active', true)->first();
    }

    /**
     * Calculate tax amount based on subtotal
     */
    public function calculateTax($subtotal)
    {
        return $subtotal * $this->rate;
    }

    /**
     * Get display rate as percentage
     */
    public function getRatePercentageAttribute()
    {
        return $this->rate * 100;
    }

    /**
     * Check if customer pays the tax
     */
    public function isCustomerPays()
    {
        return $this->application_type === 'customer_pays';
    }

    /**
     * Check if business absorbs the tax
     */
    public function isBusinessAbsorbs()
    {
        return $this->application_type === 'business_absorbs';
    }
}
