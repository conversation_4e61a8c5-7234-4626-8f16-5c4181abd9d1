<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'menu_item_id',
        'quantity',
        'price_at_time',
        'notes',
    ];

    protected function casts(): array
    {
        return [
            'price_at_time' => 'decimal:2',
        ];
    }

    /**
     * Get the order that owns this item.
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the menu item for this order item.
     */
    public function menuItem()
    {
        return $this->belongsTo(MenuItem::class);
    }

    /**
     * Get the total price for this order item.
     */
    public function getTotalPriceAttribute()
    {
        return $this->quantity * $this->price_at_time;
    }
}
