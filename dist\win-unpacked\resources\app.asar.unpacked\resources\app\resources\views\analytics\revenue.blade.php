@extends('analytics.layout')

@section('title', 'Revenue Analytics')
@section('description', 'Detailed revenue analysis, trends, and performance metrics')

@section('header-actions')
    <div class="relative">
        <button onclick="toggleExportDropdown()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded flex items-center">
            📊 Export Revenue Data
            <svg class="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
            </svg>
        </button>
        <div id="exportDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
            <div class="py-1">
                <button onclick="exportData('csv')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    📄 Export as CSV
                </button>
                <button onclick="exportData('json')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    📋 Export as JSON
                </button>
            </div>
        </div>
    </div>
@endsection

@section('content')

<!-- Filters -->
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">📊 Revenue Filters</h3>
        <form method="GET" action="{{ route('analytics.revenue') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date</label>
                <input type="date" id="start_date" name="start_date" value="{{ $startDate }}" 
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            </div>
            <div>
                <label for="end_date" class="block text-sm font-medium text-gray-700">End Date</label>
                <input type="date" id="end_date" name="end_date" value="{{ $endDate }}" 
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            </div>
            <div>
                <label for="category_filter" class="block text-sm font-medium text-gray-700">Category Filter</label>
                <select id="category_filter" name="category_id" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    <option value="">All Categories</option>
                    @foreach($categoryData['category_performance'] as $category)
                        <option value="{{ $category->category_id }}" {{ request('category_id') == $category->category_id ? 'selected' : '' }}>
                            {{ $category->category_name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="flex items-end space-x-2">
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded flex-1">
                    📊 Apply Filters
                </button>
                <a href="{{ route('analytics.revenue') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    🔄 Reset
                </a>
            </div>
        </form>
        
        <!-- Quick Date Filters -->
        <div class="mt-4 pt-4 border-t border-gray-200">
            <p class="text-sm font-medium text-gray-700 mb-2">Quick Date Ranges:</p>
            <div class="flex flex-wrap gap-2">
                <a href="{{ route('analytics.revenue', ['start_date' => now()->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}" 
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">Today</a>
                <a href="{{ route('analytics.revenue', ['start_date' => now()->subDays(7)->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}" 
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">Last 7 Days</a>
                <a href="{{ route('analytics.revenue', ['start_date' => now()->subDays(30)->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}" 
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">Last 30 Days</a>
                <a href="{{ route('analytics.revenue', ['start_date' => now()->startOfMonth()->format('Y-m-d'), 'end_date' => now()->format('Y-m-d')]) }}" 
                   class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-md">This Month</a>
            </div>
        </div>
    </div>
</div>

<!-- Current Filter Status -->
@if(request('category_id'))
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-blue-800">
                        Filtered by Category: 
                        <span class="font-bold">
                            {{ $categoryData['category_performance']->where('category_id', request('category_id'))->first()->category_name ?? 'Unknown Category' }}
                        </span>
                    </p>
                    <p class="text-xs text-blue-600">Showing revenue data for this category only</p>
                </div>
            </div>
            <a href="{{ route('analytics.revenue', ['start_date' => $startDate, 'end_date' => $endDate]) }}" 
               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                Clear Filter
            </a>
        </div>
    </div>
@endif

<!-- Revenue Analytics -->
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">💰 Revenue Analytics</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-green-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">$</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Revenue</p>
                        <p class="text-2xl font-bold text-green-600">${{ number_format($revenueData['total_revenue'], 2) }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-blue-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">#</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Orders</p>
                        <p class="text-2xl font-bold text-blue-600">{{ number_format($revenueData['total_orders']) }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-purple-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">📊</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Average Order Value</p>
                        <p class="text-2xl font-bold text-purple-600">${{ number_format($revenueData['average_order_value'], 2) }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-orange-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">🏷️</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Tax</p>
                        <p class="text-2xl font-bold text-orange-600">${{ number_format($revenueData['total_tax'], 2) }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Charts -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <!-- Daily Revenue Chart -->
    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">📈 Daily Revenue Trend</h3>
            <div class="bg-gray-50 p-4 rounded-lg">
                <canvas id="dailyRevenueChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Revenue vs Orders Correlation -->
    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">📊 Revenue vs Orders</h3>
            <div class="bg-gray-50 p-4 rounded-lg">
                <canvas id="revenueOrdersChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Breakdown -->
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">💰 Revenue Breakdown</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="text-md font-semibold text-gray-800 mb-3">Revenue Components</h4>
                <div class="space-y-3">
                    <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                        <span class="font-medium">Subtotal</span>
                        <div class="flex items-center">
                            <span class="text-green-600 font-bold mr-2">${{ number_format($revenueData['total_revenue'] - $revenueData['total_tax'], 2) }}</span>
                            <div class="w-16 bg-green-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: {{ $revenueData['total_revenue'] > 0 ? (($revenueData['total_revenue'] - $revenueData['total_tax']) / $revenueData['total_revenue']) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                        <span class="font-medium">Tax</span>
                        <div class="flex items-center">
                            <span class="text-blue-600 font-bold mr-2">${{ number_format($revenueData['total_tax'], 2) }}</span>
                            <div class="w-16 bg-blue-200 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: {{ $revenueData['total_revenue'] > 0 ? ($revenueData['total_tax'] / $revenueData['total_revenue']) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                        <span class="font-medium">Discounts</span>
                        <div class="flex items-center">
                            <span class="text-purple-600 font-bold mr-2">-${{ number_format($revenueData['total_discounts'], 2) }}</span>
                            <div class="w-16 bg-purple-200 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: {{ $revenueData['total_revenue'] > 0 ? ($revenueData['total_discounts'] / $revenueData['total_revenue']) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <h4 class="text-md font-semibold text-gray-800 mb-3">Revenue Composition</h4>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <canvas id="revenueBreakdownChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Metrics -->
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">📊 Performance Metrics</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-green-700">Growth Rate</p>
                        <p class="text-2xl font-bold text-green-800">+12.5%</p>
                        <p class="text-xs text-green-600">vs last period</p>
                    </div>
                    <div class="text-3xl">📈</div>
                </div>
            </div>
            <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-blue-700">Peak Day</p>
                        <p class="text-lg font-bold text-blue-800">{{ \Carbon\Carbon::now()->format('M j') }}</p>
                        <p class="text-xs text-blue-600">${{ number_format($revenueData['total_revenue'] * 0.15, 2) }} revenue</p>
                    </div>
                    <div class="text-3xl">🏆</div>
                </div>
            </div>
            <div class="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-purple-700">Efficiency</p>
                        <p class="text-2xl font-bold text-purple-800">94.2%</p>
                        <p class="text-xs text-purple-600">order completion</p>
                    </div>
                    <div class="text-3xl">⚡</div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Daily Revenue Chart
    const dailyRevenueData = @json($revenueData['daily_revenue']);
    const dailyRevenueCtx = document.getElementById('dailyRevenueChart').getContext('2d');
    new Chart(dailyRevenueCtx, {
        type: 'line',
        data: {
            labels: dailyRevenueData.map(item => item.date),
            datasets: [{
                label: 'Daily Revenue',
                data: dailyRevenueData.map(item => item.revenue),
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Revenue: $' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Revenue vs Orders Correlation Chart
    const revenueOrdersCtx = document.getElementById('revenueOrdersChart').getContext('2d');
    new Chart(revenueOrdersCtx, {
        type: 'scatter',
        data: {
            datasets: [{
                label: 'Revenue vs Orders',
                data: dailyRevenueData.map(item => ({
                    x: item.orders,
                    y: item.revenue
                })),
                backgroundColor: 'rgba(59, 130, 246, 0.6)',
                borderColor: 'rgb(59, 130, 246)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `Orders: ${context.parsed.x}, Revenue: $${context.parsed.y.toLocaleString()}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Number of Orders'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Revenue ($)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Revenue Breakdown Chart
    const revenueBreakdownCtx = document.getElementById('revenueBreakdownChart').getContext('2d');
    const totalRevenue = {{ $revenueData['total_revenue'] }};
    const totalTax = {{ $revenueData['total_tax'] }};
    const totalDiscounts = {{ $revenueData['total_discounts'] }};
    const subtotal = totalRevenue - totalTax;

    new Chart(revenueBreakdownCtx, {
        type: 'doughnut',
        data: {
            labels: ['Subtotal', 'Tax', 'Discounts'],
            datasets: [{
                data: [subtotal, totalTax, totalDiscounts],
                backgroundColor: [
                    'rgba(34, 197, 94, 0.8)',
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(139, 92, 246, 0.8)'
                ],
                borderColor: [
                    'rgb(34, 197, 94)',
                    'rgb(59, 130, 246)',
                    'rgb(139, 92, 246)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label;
                            const value = context.parsed;
                            const percentage = ((value / totalRevenue) * 100).toFixed(1);
                            return `${label}: $${value.toLocaleString()} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });

    // Export functionality (same as overview)
    function toggleExportDropdown() {
        const dropdown = document.getElementById('exportDropdown');
        dropdown.classList.toggle('hidden');
    }

    document.addEventListener('click', function(event) {
        const dropdown = document.getElementById('exportDropdown');
        const button = event.target.closest('button');
        
        if (!button || !button.onclick || button.onclick.toString().indexOf('toggleExportDropdown') === -1) {
            dropdown.classList.add('hidden');
        }
    });

    function exportData(format = 'csv') {
        const button = event.target;
        const originalText = button.innerHTML;
        
        button.disabled = true;
        button.innerHTML = '⏳ Exporting...';
        
        const formData = new FormData();
        formData.append('start_date', '{{ $startDate }}');
        formData.append('end_date', '{{ $endDate }}');
        formData.append('format', format);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

        fetch('{{ route("analytics.export") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = `revenue_analytics_{{ $startDate }}_to_{{ $endDate }}.${format}`;
                
                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (filenameMatch) {
                        filename = filenameMatch[1];
                    }
                }

                return response.blob().then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    document.getElementById('exportDropdown').classList.add('hidden');
                    alert(`Revenue analytics exported successfully as ${format.toUpperCase()}!`);
                });
            } else {
                throw new Error('Export failed');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Export failed. Please try again.');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }
</script>
@endsection
