<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CheckAuthCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:check-auth';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check current authentication state';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔐 Checking Authentication State');
        $this->newLine();

        // Check session data
        $this->info('📋 Session Data:');
        $sessionData = session()->all();
        foreach ($sessionData as $key => $value) {
            if (is_string($value) || is_numeric($value)) {
                $this->info("  {$key}: {$value}");
            } else {
                $this->info("  {$key}: " . gettype($value));
            }
        }

        return 0;
    }
}
