<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Menu Item Details') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('menu-items.edit', $menuItem) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Edit
                </a>
                <a href="{{ route('menu-items.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Menu Items
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    
                    <!-- Menu Item Header -->
                    <div class="mb-8">
                        <div class="flex items-start space-x-6">
                            <!-- Menu Item Image -->
                            <div class="flex-shrink-0">
                                @if($menuItem->image_path)
                                    <img src="{{ asset('storage/' . $menuItem->image_path) }}" 
                                         alt="{{ $menuItem->name }}" 
                                         class="h-32 w-32 object-cover rounded-lg border shadow-sm">
                                @else
                                    <div class="h-32 w-32 bg-gray-200 rounded-lg flex items-center justify-center border">
                                        <span class="text-gray-400 text-sm">No Image</span>
                                    </div>
                                @endif
                            </div>
                            
                            <!-- Menu Item Info -->
                            <div class="flex-1">
                                <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $menuItem->name }}</h1>
                                <p class="text-lg text-gray-600 mb-4">{{ $menuItem->description ?: 'No description available' }}</p>
                                
                                <div class="flex items-center space-x-6">
                                    <div>
                                        <span class="text-sm font-medium text-gray-500">Price</span>
                                        <p class="text-2xl font-bold text-green-600">${{ number_format($menuItem->price, 2) }}</p>
                                    </div>
                                    <div>
                                        <span class="text-sm font-medium text-gray-500">Category</span>
                                        <p class="text-lg font-semibold text-gray-900">{{ $menuItem->category->name }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Menu Item Details -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <!-- Basic Information -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                                    <dd class="text-sm text-gray-900">{{ $menuItem->name }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Category</dt>
                                    <dd class="text-sm text-gray-900">{{ $menuItem->category->name }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Price</dt>
                                    <dd class="text-sm text-gray-900">${{ number_format($menuItem->price, 2) }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Description</dt>
                                    <dd class="text-sm text-gray-900">{{ $menuItem->description ?: 'No description provided' }}</dd>
                                </div>
                            </dl>
                        </div>

                        <!-- System Information -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">System Information</h3>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Created</dt>
                                    <dd class="text-sm text-gray-900">{{ $menuItem->created_at->format('M d, Y \a\t H:i') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                                    <dd class="text-sm text-gray-900">{{ $menuItem->updated_at->format('M d, Y \a\t H:i') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Image</dt>
                                    <dd class="text-sm text-gray-900">
                                        @if($menuItem->image_path)
                                            <span class="text-green-600">✓ Image uploaded</span>
                                        @else
                                            <span class="text-gray-400">No image</span>
                                        @endif
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="text-sm text-gray-900">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                        <div class="flex space-x-3">
                            <a href="{{ route('menu-items.edit', $menuItem) }}" 
                               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200">
                                Edit Menu Item
                            </a>
                            <a href="{{ route('menu-items.index') }}" 
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition-colors duration-200">
                                Back to List
                            </a>
                        </div>
                        
                        <!-- Delete Button (Admin only) -->
                        @if(auth()->user()->isAdmin())
                            <form method="POST" action="{{ route('menu-items.destroy', $menuItem) }}" 
                                  onsubmit="return confirm('Are you sure you want to delete this menu item?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" 
                                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200">
                                    Delete
                                </button>
                            </form>
                        @endif
                    </div>

                </div>
            </div>
        </div>
    </div>
</x-app-layout>
