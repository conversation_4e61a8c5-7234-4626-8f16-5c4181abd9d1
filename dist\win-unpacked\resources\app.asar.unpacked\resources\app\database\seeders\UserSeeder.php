<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default admin user
        User::firstOrCreate(
            ['username' => 'admin'],
            [
                'name' => 'Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'Admin',
            ]
        );

        // Create default cashier user
        User::firstOrCreate(
            ['username' => 'cashier'],
            [
                'name' => 'Cashier',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'Cashier',
            ]
        );
    }
}
