<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MenuCategory;

class MenuCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON>nack',
        ];

        foreach ($categories as $category) {
            MenuCategory::firstOrCreate(['name' => $category]);
        }
    }
}
