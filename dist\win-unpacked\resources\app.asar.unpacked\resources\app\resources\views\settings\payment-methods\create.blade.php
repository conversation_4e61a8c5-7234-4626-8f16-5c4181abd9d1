<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create New Payment Method') }}
            </h2>
            <a href="{{ route('settings.payment-methods.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Back to Payment Methods
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Create New Payment Method</h3>
                        <a href="{{ route('settings.payment-methods.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                            <i class="fas fa-arrow-left"></i> Back to Payment Methods
                        </a>
                    </div>
                </div>
                <div class="p-6">
                    <form action="{{ route('settings.payment-methods.store') }}" method="POST">
                        @csrf
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700">Name <span class="text-red-500">*</span></label>
                                <input type="text" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-300 @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div>
                                <label for="sort_order" class="block text-sm font-medium text-gray-700">Sort Order</label>
                                <input type="number" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('sort_order') border-red-300 @enderror" 
                                       id="sort_order" name="sort_order" value="{{ old('sort_order') }}" min="0">
                                @error('sort_order')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Leave empty to add at the end</p>
                            </div>
                        </div>

                        <div class="mt-6">
                            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                            <textarea class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-300 @enderror" 
                                      id="description" name="description" rows="3">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <label for="icon" class="block text-sm font-medium text-gray-700">Icon (FontAwesome class)</label>
                                <input type="text" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('icon') border-red-300 @enderror" 
                                       id="icon" name="icon" value="{{ old('icon') }}" 
                                       placeholder="e.g., fas fa-credit-card">
                                @error('icon')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">
                                    Preview: <i id="icon-preview" class="{{ old('icon', 'fas fa-question') }}"></i>
                                </p>
                            </div>
                            
                            <div>
                                <label for="color" class="block text-sm font-medium text-gray-700">Color</label>
                                <input type="color" class="mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('color') border-red-300 @enderror" 
                                       id="color" name="color" value="{{ old('color', '#28a745') }}">
                                @error('color')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <div class="flex items-center">
                                    <input class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" type="checkbox" id="requires_reference" 
                                           name="requires_reference" value="1" {{ old('requires_reference') ? 'checked' : '' }}>
                                    <label class="ml-2 block text-sm text-gray-900" for="requires_reference">
                                        Requires Reference Number
                                    </label>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">Check if this payment method requires a reference number (e.g., transaction ID, check number)</p>
                            </div>
                            
                            <div>
                                <div class="flex items-center">
                                    <input class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" type="checkbox" id="is_active" 
                                           name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="ml-2 block text-sm text-gray-900" for="is_active">
                                        Active
                                    </label>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">Uncheck to disable this payment method</p>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Common Payment Method Examples</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="example-item p-2 border rounded mb-2" style="cursor: pointer;" 
                                             data-name="Cash" data-icon="fas fa-money-bill-wave" data-color="#28a745" data-reference="false">
                                            <i class="fas fa-money-bill-wave text-success me-2"></i>
                                            <strong>Cash</strong>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="example-item p-2 border rounded mb-2" style="cursor: pointer;" 
                                             data-name="Credit Card" data-icon="fas fa-credit-card" data-color="#007bff" data-reference="true">
                                            <i class="fas fa-credit-card text-primary me-2"></i>
                                            <strong>Credit Card</strong>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="example-item p-2 border rounded mb-2" style="cursor: pointer;" 
                                             data-name="Digital Wallet" data-icon="fas fa-mobile-alt" data-color="#6f42c1" data-reference="true">
                                            <i class="fas fa-mobile-alt text-purple me-2"></i>
                                            <strong>Digital Wallet</strong>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="example-item p-2 border rounded mb-2" style="cursor: pointer;" 
                                             data-name="Bank Transfer" data-icon="fas fa-university" data-color="#fd7e14" data-reference="true">
                                            <i class="fas fa-university text-warning me-2"></i>
                                            <strong>Bank Transfer</strong>
                                        </div>
                                    </div>
                                </div>
                                <small class="text-muted">Click on any example above to auto-fill the form</small>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="{{ route('settings.payment-methods.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">Cancel</a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                <i class="fas fa-save"></i> Create Payment Method
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Icon preview
    const iconInput = document.getElementById('icon');
    const iconPreview = document.getElementById('icon-preview');
    
    iconInput.addEventListener('input', function() {
        const iconClass = this.value || 'fas fa-question';
        iconPreview.className = iconClass;
    });
    
    // Color preview for icon
    const colorInput = document.getElementById('color');
    colorInput.addEventListener('input', function() {
        iconPreview.style.color = this.value;
    });
    
    // Example items click handler
    document.querySelectorAll('.example-item').forEach(item => {
        item.addEventListener('click', function() {
            const name = this.dataset.name;
            const icon = this.dataset.icon;
            const color = this.dataset.color;
            const requiresReference = this.dataset.reference === 'true';
            
            document.getElementById('name').value = name;
            document.getElementById('icon').value = icon;
            document.getElementById('color').value = color;
            document.getElementById('requires_reference').checked = requiresReference;
            
            // Update preview
            iconPreview.className = icon;
            iconPreview.style.color = color;
            
            // Highlight selected example
            document.querySelectorAll('.example-item').forEach(el => el.classList.remove('border-primary'));
            this.classList.add('border-primary');
        });
    });
});
</script>
@endpush
@endsection