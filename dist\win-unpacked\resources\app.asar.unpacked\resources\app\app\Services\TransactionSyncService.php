<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Customer;
use App\Models\MenuItem;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class TransactionSyncService
{
    private string $apiBaseUrl;
    private int $timeout;
    private int $retryAttempts;

    public function __construct()
    {
        $this->apiBaseUrl = config('pos.api_base_url', 'https://viera-filament.test/api/pos');
        $this->timeout = config('pos.api_timeout', 30);
        $this->retryAttempts = config('pos.max_retry_attempts', 3);
    }

    /**
     * Sync a single transaction to the API
     */
    public function syncTransaction(Order $order): array
    {
        try {
            Log::info('TransactionSync: Starting sync for order', ['order_id' => $order->id]);

            // Check if order is ready for sync (paid or cancelled)
            if (!in_array($order->status, ['Paid', 'Cancelled'])) {
                return [
                    'success' => false,
                    'error' => 'Order must be paid or cancelled to sync'
                ];
            }

            // Check if already synced
            if ($order->isSync) {
                return [
                    'success' => true,
                    'message' => 'Transaction already synced'
                ];
            }

            // Check if order has items
            if ($order->orderItems->isEmpty()) {
                Log::warning('TransactionSync: Order has no items, skipping sync', [
                    'order_id' => $order->id,
                    'transaction_number' => $order->transaction_number
                ]);

                // Update sync error
                $order->update([
                    'sync_error' => 'Cannot sync order without items'
                ]);

                return [
                    'success' => false,
                    'error' => 'Order has no items to sync'
                ];
            }

            // Check network connectivity
            if (!$this->isNetworkAvailable()) {
                Log::info('TransactionSync: Network unavailable, skipping sync', ['order_id' => $order->id]);
                return [
                    'success' => false,
                    'error' => 'Network unavailable'
                ];
            }

            // Get API token
            $token = $this->getApiToken();
            if (!$token) {
                throw new \Exception('No API token available');
            }

            // Prepare transaction data for API
            $transactionData = $this->prepareTransactionData($order);

            // Send to API
            $response = Http::timeout($this->timeout)
                ->withToken($token)
                ->post("{$this->apiBaseUrl}/sync/transactions", $transactionData);

            if ($response->successful()) {
                // Mark as synced
                $order->update([
                    'isSync' => true,
                    'synced_at' => now(),
                    'sync_error' => null
                ]);

                Log::info('TransactionSync: Successfully synced transaction', [
                    'order_id' => $order->id,
                    'transaction_number' => $order->transaction_number
                ]);

                return [
                    'success' => true,
                    'message' => 'Transaction synced successfully'
                ];
            } else {
                $errorMessage = $response->body();
                Log::error('TransactionSync: API error', [
                    'order_id' => $order->id,
                    'status' => $response->status(),
                    'error' => $errorMessage
                ]);

                // Update sync error
                $order->update([
                    'sync_error' => "API Error ({$response->status()}): {$errorMessage}"
                ]);

                return [
                    'success' => false,
                    'error' => "API Error: {$errorMessage}"
                ];
            }

        } catch (\Exception $e) {
            Log::error('TransactionSync: Exception during sync', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Update sync error
            $order->update([
                'sync_error' => 'Sync failed: ' . $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Sync multiple transactions in batch
     */
    public function syncMultipleTransactions(array $orderIds): array
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => []
        ];

        foreach ($orderIds as $orderId) {
            $order = Order::find($orderId);
            if (!$order) {
                $results['failed']++;
                $results['errors'][] = "Order {$orderId} not found";
                continue;
            }

            $result = $this->syncTransaction($order);
            if ($result['success']) {
                $results['success']++;
            } else {
                $results['failed']++;
                $results['errors'][] = "Order {$orderId}: " . $result['error'];
            }
        }

        return $results;
    }

    /**
     * Sync all unsynced transactions (paid and cancelled)
     */
    public function syncAllUnsyncedTransactions(): array
    {
        $unsyncedOrders = Order::whereIn('status', ['Paid', 'Cancelled'])
            ->where('isSync', false)
            ->get();
        
        Log::info('TransactionSync: Starting batch sync', [
            'unsynced_count' => $unsyncedOrders->count()
        ]);

        if ($unsyncedOrders->isEmpty()) {
            return [
                'success' => true,
                'message' => 'No transactions to sync',
                'stats' => ['success' => 0, 'failed' => 0]
            ];
        }

        $results = $this->syncMultipleTransactions($unsyncedOrders->pluck('id')->toArray());

        Log::info('TransactionSync: Batch sync completed', $results);

        return [
            'success' => $results['failed'] === 0,
            'message' => "Synced {$results['success']} transactions, {$results['failed']} failed",
            'stats' => $results
        ];
    }

    /**
     * Prepare transaction data for API according to the required format
     */
    private function prepareTransactionData(Order $order): array
    {
        $order->load(['customer', 'orderItems.menuItem', 'payments', 'table']);

        // Calculate amounts
        $totalPaid = $order->payments->sum('amount_paid');
        $change = max(0, $totalPaid - $order->total_amount);

        // Get primary payment method (use the first payment method if multiple)
        $primaryPayment = $order->payments->first();
        $paymentMethod = $primaryPayment ? $primaryPayment->method : 'Cash';

        // Get outlet ID from session or cache
        $outletId = session('api_outlet_id') ?: \Cache::get('pos_outlet_id');

        // Map order type to API format
        $orderType = $this->mapOrderTypeToApi($order->order_type);

        // Get table number - use table name if available, otherwise null
        $tableNumber = null;
        if ($order->table) {
            $tableNumber = $order->table->name ?? $order->table->table_number ?? null;
        }

        // Prepare items data
        $items = [];
        foreach ($order->orderItems as $orderItem) {
            // Use API ID if available, otherwise skip this item (shouldn't happen in normal flow)
            $productId = $orderItem->menuItem->api_id;

            if (!$productId) {
                Log::warning('TransactionSync: Menu item missing API ID', [
                    'order_id' => $order->id,
                    'menu_item_id' => $orderItem->menu_item_id,
                    'menu_item_name' => $orderItem->menuItem->name ?? 'Unknown'
                ]);
                // Skip items without API ID as they can't be synced
                continue;
            }

            $items[] = [
                'product_id' => $productId,
                'quantity' => $orderItem->quantity,
                'unit_price' => (float) $orderItem->price_at_time,
                'discount_per_item' => 0.00 // Currently not implemented per-item discounts
            ];
        }

        // Validate that we have at least one item to sync
        if (empty($items)) {
            throw new \Exception('No items with valid API IDs found for transaction sync. Please ensure all menu items are synced to the API before attempting transaction sync.');
        }

        return [
            'transactions' => [
                [
                    'transaction_number' => $order->transaction_number,
                    'customer_id' => $order->customer->api_id ?? null,
                    'outlet_id' => $outletId,
                    'order_type' => $orderType,
                    'transaction_date' => $order->created_at->setTimezone('Asia/Jakarta')->toISOString(),
                    'total_amount' => (float) $order->total_amount,
                    'discount_amount' => (float) ($order->discount_amount ?? 0),
                    'tax_amount' => (float) ($order->tax_amount ?? 0),
                    'net_amount' => (float) $order->total_amount,
                    'payment_method' => $paymentMethod,
                    'amount_paid' => (float) $totalPaid,
                    'change_given' => (float) $change,
                    'table_number' => $tableNumber,
                    'items' => $items
                ]
            ]
        ];
    }

    /**
     * Map local order type to API format
     */
    private function mapOrderTypeToApi(string $orderType): string
    {
        $mapping = [
            'dine_in' => 'dine_in',
            'take_away' => 'takeaway',
            'takeaway' => 'takeaway',
            'delivery' => 'delivery',
            'pickup' => 'pickup'
        ];

        return $mapping[strtolower($orderType)] ?? 'dine_in';
    }

    /**
     * Get cached authentication token or authenticate if needed
     */
    private function getApiToken(): ?string
    {
        // First try to get token from session (if user is logged in)
        if (session()->has('api_token')) {
            $sessionToken = session('api_token');
            if ($sessionToken) {
                return $sessionToken;
            }
        }

        // Fallback to cached token for background processes
        $cachedToken = Cache::get('pos_api_token');
        if ($cachedToken) {
            return $cachedToken;
        }

        return null;
    }

    /**
     * Check if network is available for API calls
     */
    private function isNetworkAvailable(): bool
    {
        try {
            // For development, be more lenient with network checks
            if (app()->environment('local')) {
                return $this->isNetworkAvailableForDevelopment();
            }

            // Production network check
            return $this->isNetworkAvailableForProduction();
        } catch (\Exception $e) {
            Log::warning('TransactionSync: Network check failed', ['error' => $e->getMessage()]);

            // In development, allow sync even if network check fails
            if (app()->environment('local')) {
                return true;
            }

            return false;
        }
    }

    /**
     * Network check for development environment
     */
    private function isNetworkAvailableForDevelopment(): bool
    {
        try {
            // In development, first try a simple connectivity test
            $response = Http::timeout(5)->get('https://www.google.com/favicon.ico');
            if ($response->successful()) {
                return true;
            }

            // If Google fails, try the API directly (might be local)
            return $this->testApiConnectivity();
        } catch (\Exception $e) {
            Log::info('TransactionSync: Development network check failed, allowing sync anyway', [
                'error' => $e->getMessage()
            ]);
            // In development, be very permissive - allow sync even if network check fails
            return true;
        }
    }

    /**
     * Network check for production environment
     */
    private function isNetworkAvailableForProduction(): bool
    {
        try {
            // First try a simple connectivity test to a reliable endpoint
            $response = Http::timeout(3)
                ->get('https://www.google.com/favicon.ico');

            if ($response->successful()) {
                // If basic internet works, test API connectivity
                return $this->testApiConnectivity();
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Test API connectivity specifically
     */
    private function testApiConnectivity(): bool
    {
        try {
            // Try to access the API base URL or a known endpoint
            $testEndpoints = [
                'https://viera-filament.test/api/pos/sync/transactions', // The actual endpoint we'll use
                $this->apiBaseUrl . '/status',
                $this->apiBaseUrl . '/health',
                $this->apiBaseUrl,
                'https://viera-filament.test/api/pos'
            ];

            foreach ($testEndpoints as $endpoint) {
                try {
                    $response = Http::timeout(5)->get($endpoint);
                    // Accept any response (even 404/401) as long as we can reach the server
                    if ($response->status() < 500) {
                        Log::info('TransactionSync: API connectivity test successful', [
                            'endpoint' => $endpoint,
                            'status' => $response->status()
                        ]);
                        return true;
                    }
                } catch (\Exception $e) {
                    Log::debug('TransactionSync: API endpoint failed', [
                        'endpoint' => $endpoint,
                        'error' => $e->getMessage()
                    ]);
                    continue;
                }
            }

            Log::warning('TransactionSync: All API endpoints failed connectivity test');
            return false;
        } catch (\Exception $e) {
            Log::error('TransactionSync: API connectivity test exception', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get sync statistics
     */
    public function getSyncStats(): array
    {
        return [
            'total_syncable_orders' => Order::whereIn('status', ['Paid', 'Cancelled'])->count(),
            'synced_transactions' => Order::whereIn('status', ['Paid', 'Cancelled'])->where('isSync', true)->count(),
            'unsynced_transactions' => Order::whereIn('status', ['Paid', 'Cancelled'])->where('isSync', false)->count(),
            'failed_transactions' => Order::whereIn('status', ['Paid', 'Cancelled'])->whereNotNull('sync_error')->count(),
            'last_sync' => Order::where('isSync', true)->max('synced_at')
        ];
    }
}
