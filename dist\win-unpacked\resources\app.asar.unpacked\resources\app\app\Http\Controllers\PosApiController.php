<?php

namespace App\Http\Controllers;

use App\Services\PosApiService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;

/**
 * Controller for handling POS API operations
 */
class PosApiController extends Controller
{
    private PosApiService $posApiService;

    public function __construct(PosApiService $posApiService)
    {
        $this->posApiService = $posApiService;
    }

    /**
     * Fetch products from POS API
     * 
     * @return JsonResponse
     */
    public function fetchProducts(): JsonResponse
    {
        $result = $this->posApiService->fetchProducts();
        
        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Products fetched successfully',
                'data' => $result['data'],
                'count' => count($result['data']['data'] ?? [])
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to fetch products',
            'error' => $result['error']
        ], $result['status_code'] ?? 500);
    }

    /**
     * Fetch products with caching
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function fetchProductsWithCache(Request $request): JsonResponse
    {
        $baseCacheKey = 'pos_products';
        $cacheDuration = $request->get('cache_duration', 300); // 5 minutes default

        // Include outlet ID in cache key for outlet-specific caching
        $outletId = session('api_outlet_id');
        $cacheKey = $outletId ? $baseCacheKey . '_outlet_' . $outletId : $baseCacheKey;

        $products = Cache::remember($cacheKey, $cacheDuration, function () {
            return $this->posApiService->fetchProducts();
        });

        if ($products['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Products fetched successfully (cached)',
                'data' => $products['data'],
                'count' => count($products['data']['data'] ?? []),
                'cached' => Cache::has($cacheKey)
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to fetch products',
            'error' => $products['error']
        ], $products['status_code'] ?? 500);
    }

    /**
     * Get formatted products
     * 
     * @return JsonResponse
     */
    public function getFormattedProducts(): JsonResponse
    {
        $result = $this->posApiService->getFormattedProducts();
        
        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Formatted products retrieved successfully',
                'products' => $result['products'],
                'total_count' => $result['total_count']
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to get formatted products',
            'error' => $result['error']
        ], 500);
    }

    /**
     * Test API connection
     * 
     * @return JsonResponse
     */
    public function testConnection(): JsonResponse
    {
        $result = $this->posApiService->testConnection();
        
        return response()->json([
            'success' => $result['success'],
            'message' => $result['message'],
            'status_code' => $result['status_code'] ?? null,
            'response_time' => $result['response_time'] ?? null
        ], $result['success'] ? 200 : 500);
    }

    /**
     * Sync products to local database (example)
     * 
     * @return JsonResponse
     */
    public function syncProducts(): JsonResponse
    {
        $result = $this->posApiService->fetchProducts();
        
        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch products from API',
                'error' => $result['error']
            ], 500);
        }

        $products = $result['data']['data'] ?? [];
        $syncedCount = 0;
        $errors = [];

        foreach ($products as $productData) {
            try {
                // Example: Save to local database
                // You would implement your own logic here
                // MenuItems::updateOrCreate(
                //     ['external_id' => $productData['id']],
                //     [
                //         'name' => $productData['name'],
                //         'price' => $productData['price'],
                //         'description' => $productData['description'],
                //         'is_active' => $productData['is_active'],
                //         // ... other fields
                //     ]
                // );
                
                $syncedCount++;
            } catch (\Exception $e) {
                $errors[] = [
                    'product_id' => $productData['id'] ?? 'unknown',
                    'error' => $e->getMessage()
                ];
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Products sync completed',
            'synced_count' => $syncedCount,
            'total_products' => count($products),
            'errors' => $errors
        ]);
    }

    /**
     * Get API status and statistics
     * 
     * @return JsonResponse
     */
    public function getApiStatus(): JsonResponse
    {
        $connectionTest = $this->posApiService->testConnection();
        $lastSync = Cache::get('pos_last_sync_time');
        $cachedProducts = Cache::get('pos_products');

        return response()->json([
            'api_connection' => [
                'status' => $connectionTest['success'] ? 'connected' : 'disconnected',
                'response_time' => $connectionTest['response_time'] ?? null,
                'last_checked' => now()->toISOString()
            ],
            'cache_info' => [
                'has_cached_products' => !is_null($cachedProducts),
                'cached_products_count' => $cachedProducts ? count($cachedProducts['data']['data'] ?? []) : 0,
                'last_sync' => $lastSync
            ],
            'endpoints' => [
                'fetch_products' => route('pos-api.fetch-products'),
                'test_connection' => route('pos-api.test-connection'),
                'sync_products' => route('pos-api.sync-products')
            ]
        ]);
    }
}
