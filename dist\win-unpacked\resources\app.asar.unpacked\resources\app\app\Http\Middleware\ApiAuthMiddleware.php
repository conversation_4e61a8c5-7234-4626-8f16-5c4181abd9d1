<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ApiAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // First check if user is authenticated with <PERSON><PERSON>'s auth system
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        // Check if user has API authentication in session
        if (!session()->has('api_token') || !session('api_authenticated')) {
            Log::warning('User authenticated locally but missing API token', [
                'user_id' => Auth::id(),
                'email' => Auth::user()->email
            ]);

            // Clear the local authentication and redirect to login
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route('login')->with('error', 'API authentication required. Please log in again.');
        }

        // Validate that the API token is still valid (optional check)
        $apiToken = session('api_token');
        if (empty($apiToken)) {
            Log::warning('Empty API token in session', [
                'user_id' => Auth::id(),
                'email' => Auth::user()->email
            ]);

            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route('login')->with('error', 'Invalid API token. Please log in again.');
        }

        return $next($request);
    }
}
