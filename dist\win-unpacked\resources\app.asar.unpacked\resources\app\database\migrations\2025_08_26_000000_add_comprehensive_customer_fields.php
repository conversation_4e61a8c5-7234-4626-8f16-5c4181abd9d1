<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            // Customer Information fields
            $table->date('date_of_birth')->nullable()->after('email');
            $table->enum('gender', ['male', 'female', 'other'])->nullable()->after('date_of_birth');
            $table->string('customer_segment')->nullable()->after('gender');
            
            // Address Information fields
            $table->text('detailed_address')->nullable()->after('address');
            $table->string('postal_code', 10)->nullable()->after('detailed_address');
            
            // Business Information fields
            $table->integer('loyalty_points')->default(0)->after('points');
            $table->boolean('active_status')->default(true)->after('loyalty_points');
            $table->text('notes')->nullable()->after('active_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropColumn([
                'date_of_birth',
                'gender', 
                'customer_segment',
                'detailed_address',
                'postal_code',
                'loyalty_points',
                'active_status',
                'notes'
            ]);
        });
    }
};
