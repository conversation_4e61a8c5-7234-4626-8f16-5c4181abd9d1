<?php

namespace App\Services;

use App\Models\Order;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class OrderSyncService
{
    private string $apiBaseUrl;
    private int $timeout;

    public function __construct()
    {
        $this->apiBaseUrl = config('pos.api_base_url', 'https://viera-filament.test/api/pos');
        $this->timeout = config('pos.api_timeout', 30);
    }

    /**
     * Sync a single order to the API
     */
    public function syncOrder(Order $order): array
    {
        try {
            Log::info('OrderSync: Starting sync for order', ['order_id' => $order->id]);

            // Get API token
            $token = $this->getApiToken();
            if (!$token) {
                throw new \Exception('No API token available');
            }

            // Prepare order data for API
            $orderData = $this->prepareOrderData($order);

            // Send to API
            $response = Http::timeout($this->timeout)
                ->withToken($token)
                ->post("{$this->apiBaseUrl}/orders", $orderData);

            if ($response->successful()) {
                $order->markAsSynced();
                
                Log::info('OrderSync: Order synced successfully', [
                    'order_id' => $order->id,
                    'api_response' => $response->json()
                ]);

                return [
                    'success' => true,
                    'message' => 'Order synced successfully',
                    'api_response' => $response->json()
                ];
            } else {
                $error = "API Error: {$response->status()} - " . $response->body();
                $order->markSyncFailed($error);
                
                Log::error('OrderSync: API request failed', [
                    'order_id' => $order->id,
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);

                return [
                    'success' => false,
                    'error' => $error
                ];
            }

        } catch (\Exception $e) {
            $error = "Sync failed: " . $e->getMessage();
            $order->markSyncFailed($error);
            
            Log::error('OrderSync: Exception occurred', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $error
            ];
        }
    }

    /**
     * Sync multiple orders in batch
     */
    public function syncMultipleOrders(array $orderIds): array
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => []
        ];

        foreach ($orderIds as $orderId) {
            $order = Order::find($orderId);
            if (!$order) {
                $results['failed']++;
                $results['errors'][] = "Order {$orderId} not found";
                continue;
            }

            $result = $this->syncOrder($order);
            if ($result['success']) {
                $results['success']++;
            } else {
                $results['failed']++;
                $results['errors'][] = "Order {$orderId}: " . $result['error'];
            }
        }

        return $results;
    }

    /**
     * Sync all unsynced orders
     */
    public function syncAllUnsyncedOrders(): array
    {
        $unsyncedOrders = Order::unsynced()->get();
        
        Log::info('OrderSync: Starting batch sync', [
            'unsynced_count' => $unsyncedOrders->count()
        ]);

        if ($unsyncedOrders->isEmpty()) {
            return [
                'success' => true,
                'message' => 'No orders to sync',
                'stats' => ['success' => 0, 'failed' => 0]
            ];
        }

        $results = $this->syncMultipleOrders($unsyncedOrders->pluck('id')->toArray());

        Log::info('OrderSync: Batch sync completed', $results);

        return [
            'success' => $results['failed'] === 0,
            'message' => "Synced {$results['success']} orders, {$results['failed']} failed",
            'stats' => $results
        ];
    }

    /**
     * Prepare order data for API submission
     */
    private function prepareOrderData(Order $order): array
    {
        $order->load(['orderItems.menuItem', 'customer', 'table', 'payments', 'activeDiscounts']);

        return [
            'local_order_id' => $order->id,
            'cashier_id' => $order->api_user_id,
            'cashier_name' => $order->cashier_name,
            'cashier_email' => $order->cashier_email,
            'customer_id' => $order->customer->api_id ?? null,
            'customer_name' => $order->customer->name,
            'table_id' => $order->table->id ?? null,
            'table_name' => $order->table->name ?? null,
            'order_type' => $order->order_type,
            'party_size' => $order->party_size,
            'status' => $order->status,
            'subtotal_amount' => $order->getSubtotalAmount(),
            'discount_amount' => $order->getTotalDiscountAmount(),
            'tax_amount' => $order->tax_amount,
            'service_charge_amount' => $order->service_charge_amount ?? 0,
            'total_amount' => $order->total_amount,
            'created_at' => $order->created_at->setTimezone('Asia/Jakarta')->toISOString(),
            'completed_at' => $order->updated_at->setTimezone('Asia/Jakarta')->toISOString(),
            'items' => $order->orderItems->map(function ($item) {
                return [
                    'menu_item_id' => $item->menuItem->api_id ?? $item->menu_item_id,
                    'menu_item_name' => $item->menuItem->name ?? 'Unknown Item',
                    'quantity' => $item->quantity,
                    'price_at_time' => $item->price_at_time,
                    'total_price' => $item->quantity * $item->price_at_time,
                    'notes' => $item->notes
                ];
            })->toArray(),
            'payments' => $order->payments->map(function ($payment) {
                return [
                    'method' => $payment->method,
                    'amount_paid' => $payment->amount_paid,
                    'paid_at' => $payment->created_at->setTimezone('Asia/Jakarta')->toISOString()
                ];
            })->toArray(),
            'discounts' => $order->activeDiscounts->map(function ($discount) {
                return [
                    'type' => $discount->type,
                    'value' => $discount->value,
                    'amount' => $discount->amount,
                    'description' => $discount->description,
                    'coupon_code' => $discount->coupon_code,
                    'points_used' => $discount->points_used
                ];
            })->toArray()
        ];
    }

    /**
     * Get API token from cache or session
     */
    private function getApiToken(): ?string
    {
        // Try cache first
        $token = Cache::get('pos_api_token');
        if ($token) {
            return $token;
        }

        // Try session
        $token = session('api_token');
        if ($token) {
            // Cache it for future use
            Cache::put('pos_api_token', $token, now()->addHours(1));
            return $token;
        }

        return null;
    }

    /**
     * Get sync statistics
     */
    public function getSyncStats(): array
    {
        return [
            'total_orders' => Order::whereIn('status', ['Completed', 'Paid'])->count(),
            'synced_orders' => Order::where('isSync', true)->count(),
            'unsynced_orders' => Order::unsynced()->count(),
            'failed_orders' => Order::syncFailed()->count(),
            'last_sync' => Order::where('isSync', true)->max('synced_at')
        ];
    }
}
