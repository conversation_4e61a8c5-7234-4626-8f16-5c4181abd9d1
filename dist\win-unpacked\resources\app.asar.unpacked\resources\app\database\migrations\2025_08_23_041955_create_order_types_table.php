<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_types', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "Dine In", "Take Away", "Delivery"
            $table->string('slug')->unique(); // e.g., "dine_in", "take_away", "delivery"
            $table->string('description')->nullable();
            $table->boolean('requires_table')->default(false); // Whether this order type requires a table
            $table->boolean('requires_party_size')->default(false); // Whether this order type requires party size
            $table->string('icon')->nullable(); // CSS class or icon name
            $table->string('color')->default('#6B7280'); // Hex color for UI
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        // Insert default order types
        DB::table('order_types')->insert([
            [
                'name' => 'Dine In',
                'slug' => 'dine_in',
                'description' => 'Customer dines at the restaurant',
                'requires_table' => true,
                'requires_party_size' => true,
                'icon' => 'restaurant',
                'color' => '#3B82F6',
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Take Away',
                'slug' => 'take_away',
                'description' => 'Customer picks up the order',
                'requires_table' => false,
                'requires_party_size' => false,
                'icon' => 'shopping-bag',
                'color' => '#F59E0B',
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Delivery',
                'slug' => 'delivery',
                'description' => 'Order delivered to customer',
                'requires_table' => false,
                'requires_party_size' => false,
                'icon' => 'truck',
                'color' => '#8B5CF6',
                'is_active' => true,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_types');
    }
};
