<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kitchen Ticket - Order #{{ $order->id }}</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .ticket {
            max-width: 300px;
            margin: 0 auto;
            border: 2px solid #000;
            padding: 15px;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .order-info {
            margin-bottom: 15px;
        }
        .order-info div {
            margin-bottom: 3px;
        }
        .items {
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
            padding: 10px 0;
            margin: 15px 0;
        }
        .item {
            margin-bottom: 8px;
            padding-bottom: 5px;
            border-bottom: 1px dashed #ccc;
        }
        .item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .item-name {
            font-weight: bold;
            font-size: 14px;
        }
        .item-qty {
            float: right;
            font-weight: bold;
        }
        .item-notes {
            font-style: italic;
            color: #666;
            margin-top: 2px;
        }
        .footer {
            text-align: center;
            margin-top: 15px;
            font-size: 10px;
        }
        @media print {
            body { margin: 0; padding: 10px; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="no-print" style="text-align: center; margin-bottom: 20px;">
        <button onclick="window.print()" style="background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
            Print Kitchen Ticket
        </button>
        <button onclick="window.close()" style="background: #f44336; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; margin-left: 10px;">
            Close
        </button>
    </div>

    <div class="ticket">
        <div class="header">
            <div class="title">KITCHEN TICKET</div>
            <div>Order #{{ $order->id }}</div>
        </div>

        <div class="order-info">
            <div><strong>{{ $order->table ? 'Table:' : 'Order Type:' }}</strong> {{ $order->table ? $order->table->name : ucfirst($order->order_type ?? 'Take Away') }}</div>
            <div><strong>Party Size:</strong> {{ $order->party_size ?? 2 }} {{ ($order->party_size ?? 2) == 1 ? 'guest' : 'guests' }}</div>
            <div><strong>Time:</strong> {{ $order->created_at->setTimezone('Asia/Jakarta')->format('M d, Y H:i') }}</div>
            <div><strong>Cashier:</strong> {{ $order->cashier_name ?? 'Unknown' }}</div>
        </div>

        <div class="items">
            @foreach($order->orderItems as $item)
                <div class="item">
                    <div class="item-name">
                        @if($item->menuItem)
                            {{ $item->menuItem->name }}
                        @else
                            [Deleted Item - ID: {{ $item->menu_item_id }}]
                        @endif
                        <span class="item-qty">x{{ $item->quantity }}</span>
                    </div>
                    @if($item->notes)
                        <div class="item-notes">
                            Note: {{ $item->notes }}
                        </div>
                    @endif
                </div>
            @endforeach
        </div>

        <div class="footer">
            <div>Printed: {{ now()->setTimezone('Asia/Jakarta')->format('M d, Y H:i:s') }}</div>
            <div style="margin-top: 10px; font-weight: bold;">
                === KITCHEN COPY ===
            </div>
        </div>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
