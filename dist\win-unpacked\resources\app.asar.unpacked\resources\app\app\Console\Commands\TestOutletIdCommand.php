<?php

namespace App\Console\Commands;

use App\Services\PosApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class TestOutletIdCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:test-outlet-id';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test outlet ID functionality in POS API integration';

    /**
     * Execute the console command.
     */
    public function handle(PosApiService $posApiService): int
    {
        $this->info('🏪 Testing Outlet ID Functionality');
        $this->newLine();

        // Test authentication and outlet ID capture
        $this->info('🔐 Testing Authentication...');
        $authResult = $posApiService->authenticate();
        
        if ($authResult['success']) {
            $this->info('✅ Authentication: SUCCESS');
            $this->info("   Token Length: " . strlen($authResult['token']));

            // Show full authentication response for debugging
            $this->info("📋 Full Authentication Response:");
            $this->info(json_encode($authResult['data'], JSON_PRETTY_PRINT));

            // Check outlet category for table feature determination
            if (isset($authResult['data']['user']['outlets']) && !empty($authResult['data']['user']['outlets'])) {
                $outlet = $authResult['data']['user']['outlets'][0];
                $this->info("🏪 Outlet Analysis:");
                $this->info("   Name: " . ($outlet['name'] ?? 'N/A'));
                $this->info("   Code: " . ($outlet['code'] ?? 'N/A'));

                // Check for category indicators
                $needsTables = $this->determineTableRequirement($outlet);
                $this->info("   Needs Tables: " . ($needsTables ? 'YES (FnB outlet)' : 'NO (VOO outlet)'));
            }

            // Check if outlet data is in response
            if (isset($authResult['data']['outlet'])) {
                $outlet = $authResult['data']['outlet'];
                $this->info("✅ Outlet Data Found:");
                $this->info("   Outlet ID: " . ($outlet['id'] ?? 'N/A'));
                $this->info("   Outlet Name: " . ($outlet['name'] ?? 'N/A'));

                // Check if outlet ID is cached
                $cachedOutletId = Cache::get('pos_outlet_id');
                if ($cachedOutletId) {
                    $this->info("✅ Outlet ID Cached: " . $cachedOutletId);
                } else {
                    $this->warn("⚠️  Outlet ID not found in cache");
                }
            } else {
                $this->warn("⚠️  No outlet data found in authentication response");
                $this->info("   Available keys: " . implode(', ', array_keys($authResult['data'] ?? [])));
            }
        } else {
            $this->error('❌ Authentication: FAILED');
            $this->error("   Error: {$authResult['error']}");
            return 1;
        }

        $this->newLine();

        // Test product fetching with outlet ID
        $this->info('📦 Testing Product Fetching with Outlet ID...');

        // Show what outlet ID we're using
        $outletId = session('api_outlet_id') ?: \Cache::get('pos_outlet_id');
        $this->info("   Using Outlet ID: " . ($outletId ?: 'None'));

        $productsResult = $posApiService->fetchProducts();

        if ($productsResult['success']) {
            $this->info('✅ Product Fetching: SUCCESS');
            $products = $productsResult['data']['products'] ?? $productsResult['data']['data'] ?? [];
            $productCount = count($products);
            $this->info("   Products Count: " . $productCount);

            // Show the full response structure for debugging
            if ($productCount === 0) {
                $this->warn("   📋 Full API Response (first 500 chars):");
                $responseStr = json_encode($productsResult['data'], JSON_PRETTY_PRINT);
                $this->info(substr($responseStr, 0, 500) . (strlen($responseStr) > 500 ? '...' : ''));
            }

            // Show first few products for verification
            if ($productCount > 0) {
                $this->info("   Sample Products:");
                $sampleProducts = array_slice($products, 0, 3);
                foreach ($sampleProducts as $product) {
                    $this->info("   - " . ($product['name'] ?? 'Unknown') . " (ID: " . ($product['id'] ?? 'N/A') . ", Price: " . ($product['price'] ?? 'N/A') . ")");
                }
            }
        } else {
            $this->error('❌ Product Fetching: FAILED');
            $this->error("   Error: {$productsResult['error']}");
        }

        $this->newLine();

        // Test cache functionality
        $this->info('💾 Testing Cache Functionality...');
        $cacheKeys = [
            'pos_api_token' => Cache::get('pos_api_token'),
            'pos_outlet_id' => Cache::get('pos_outlet_id'),
            'pos_products' => Cache::has('pos_products'),
        ];

        foreach ($cacheKeys as $key => $value) {
            if ($value) {
                if ($key === 'pos_products') {
                    $this->info("✅ Cache Key '{$key}': EXISTS");
                } else {
                    $displayValue = $key === 'pos_api_token' ? '***' : $value;
                    $this->info("✅ Cache Key '{$key}': {$displayValue}");
                }
            } else {
                $this->warn("⚠️  Cache Key '{$key}': NOT FOUND");
            }
        }

        $this->newLine();
        $this->info('🎉 Outlet ID functionality test completed!');
        
        return 0;
    }

    /**
     * Determine if outlet requires table functionality
     */
    private function determineTableRequirement(array $outlet): bool
    {
        $outletName = $outlet['name'] ?? '';
        $outletCode = $outlet['code'] ?? '';

        // Check if outlet is FnB (Food & Beverage) - needs tables
        if (str_contains(strtolower($outletName), 'fnb') ||
            str_contains(strtolower($outletName), 'food') ||
            str_contains(strtolower($outletName), 'restaurant') ||
            str_contains(strtolower($outletName), 'cafe')) {
            return true;
        }

        // Check if outlet is VOO (Viera Oleh-Oleh) - doesn't need tables
        if (str_contains(strtolower($outletName), 'voo') ||
            str_contains(strtolower($outletName), 'oleh-oleh') ||
            str_contains(strtolower($outletName), 'souvenir')) {
            return false;
        }

        // Default to requiring tables for unknown outlet types
        return true;
    }
}
