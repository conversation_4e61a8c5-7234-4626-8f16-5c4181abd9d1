<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_methods', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "Cash", "Credit Card", "Debit Card"
            $table->string('slug')->unique(); // e.g., "cash", "credit_card", "debit_card"
            $table->string('description')->nullable();
            $table->string('icon')->nullable(); // CSS class or icon name
            $table->string('color')->default('#6B7280'); // Hex color for UI
            $table->boolean('requires_reference')->default(false); // Whether this method requires a reference number
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        // Insert default payment methods
        DB::table('payment_methods')->insert([
            [
                'name' => 'Cash',
                'slug' => 'cash',
                'description' => 'Payment by cash',
                'icon' => 'cash',
                'color' => '#10B981',
                'requires_reference' => false,
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Credit Card',
                'slug' => 'credit_card',
                'description' => 'Payment by credit card',
                'icon' => 'credit-card',
                'color' => '#3B82F6',
                'requires_reference' => true,
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Debit Card',
                'slug' => 'debit_card',
                'description' => 'Payment by debit card',
                'icon' => 'credit-card',
                'color' => '#F59E0B',
                'requires_reference' => true,
                'is_active' => true,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Digital Wallet',
                'slug' => 'digital_wallet',
                'description' => 'Payment by digital wallet (e.g., PayPal, Apple Pay)',
                'icon' => 'device-phone-mobile',
                'color' => '#8B5CF6',
                'requires_reference' => true,
                'is_active' => true,
                'sort_order' => 4,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};
