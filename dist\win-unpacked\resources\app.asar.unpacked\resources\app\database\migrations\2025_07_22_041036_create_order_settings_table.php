<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value');
            $table->string('type')->default('string'); // string, number, boolean, json
            $table->string('category')->default('general'); // tax, service, discount, general
            $table->string('label');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Insert default settings
        DB::table('order_settings')->insert([
            [
                'key' => 'tax_rate',
                'value' => '0.10',
                'type' => 'number',
                'category' => 'tax',
                'label' => 'Tax Rate (%)',
                'description' => 'Default tax rate applied to orders (as decimal, e.g., 0.10 for 10%)',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'service_charge_rate',
                'value' => '0.00',
                'type' => 'number',
                'category' => 'service',
                'label' => 'Service Charge Rate (%)',
                'description' => 'Service charge rate applied to orders (as decimal, e.g., 0.05 for 5%)',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'auto_apply_tax',
                'value' => 'true',
                'type' => 'boolean',
                'category' => 'tax',
                'label' => 'Auto Apply Tax',
                'description' => 'Automatically apply tax to all orders',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'auto_apply_service_charge',
                'value' => 'false',
                'type' => 'boolean',
                'category' => 'service',
                'label' => 'Auto Apply Service Charge',
                'description' => 'Automatically apply service charge to all orders',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'max_discount_percentage',
                'value' => '50',
                'type' => 'number',
                'category' => 'discount',
                'label' => 'Maximum Discount Percentage',
                'description' => 'Maximum percentage discount allowed on orders',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'currency_symbol',
                'value' => '$',
                'type' => 'string',
                'category' => 'general',
                'label' => 'Currency Symbol',
                'description' => 'Currency symbol displayed in the system',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'receipt_footer_text',
                'value' => 'Thank you for dining with us!',
                'type' => 'string',
                'category' => 'general',
                'label' => 'Receipt Footer Text',
                'description' => 'Text displayed at the bottom of receipts',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'order_timeout_minutes',
                'value' => '30',
                'type' => 'number',
                'category' => 'general',
                'label' => 'Order Timeout (Minutes)',
                'description' => 'Minutes after which orders are considered overdue',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'tax_charging_method',
                'value' => 'customer_pays',
                'type' => 'select',
                'category' => 'tax',
                'label' => 'Tax Charging Method',
                'description' => 'Choose whether customers pay tax separately or business absorbs tax in prices',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_settings');
    }
};
