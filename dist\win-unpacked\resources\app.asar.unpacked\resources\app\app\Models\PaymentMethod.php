<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'color',
        'requires_reference',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'requires_reference' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Scope for active payment methods
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordering by sort_order and name
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Find payment method by slug
     */
    public static function findBySlug($slug)
    {
        return static::where('slug', $slug)->first();
    }

    /**
     * Check if this payment method requires a reference number
     */
    public function requiresReference(): bool
    {
        return $this->requires_reference;
    }

    /**
     * Get the display name with icon
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name;
    }

    /**
     * Get payments using this method
     */
    public function payments()
    {
        return $this->hasMany(Payment::class, 'payment_method', 'slug');
    }
}
