<?php

namespace App\Services;

use App\Models\Customer;
use App\Services\CustomerApiService;
use App\Services\CustomerSyncService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class CustomerService
{
    private CustomerApiService $customerApiService;
    private CustomerSyncService $customerSyncService;
    private bool $useLocalFallback;

    public function __construct(CustomerApiService $customerApiService, CustomerSyncService $customerSyncService)
    {
        $this->customerApiService = $customerApiService;
        $this->customerSyncService = $customerSyncService;
        $this->useLocalFallback = true; // Always allow local fallback
    }

    /**
     * Get all customers from local database
     */
    public function getCustomers(): array
    {
        try {
            Log::info('CustomerService: Using local database customers');
            $localCustomers = Customer::orderBy('name')->get()->toArray();
            $result = $this->transformCustomersForDisplay($localCustomers, 'local');
            Log::debug('CustomerService: Local customers transformed', ['count' => count($result), 'sample' => $result[0] ?? null]);
            return $result;
        } catch (\Exception $e) {
            Log::error('CustomerService: Error in getCustomers', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get a specific customer by ID
     */
    public function getCustomer($customerId): ?object
    {
        // First check in cached customers
        $customers = $this->getCustomers();
        
        foreach ($customers as $customer) {
            if ($customer->id == $customerId) {
                return $customer;
            }
        }

        // If not found and using local fallback, try local database directly
        if ($this->useLocalFallback) {
            $localCustomer = Customer::find($customerId);
            if ($localCustomer) {
                return (object) $localCustomer->toArray();
            }
        }

        return null;
    }

    /**
     * Transform customers for display
     */
    private function transformCustomersForDisplay(array $customers, string $source): array
    {
        return array_map(function ($customer) use ($source) {
            // Ensure customer is an array first
            if (is_object($customer)) {
                $customer = (array) $customer;
            }

            // Create standardized customer object with all required properties
            $customerObj = (object) [
                'id' => $customer['id'] ?? null,
                'name' => $customer['name'] ?? 'Unknown Customer',
                'phone' => $customer['phone'] ?? '',
                'email' => $customer['email'] ?? null,
                'date_of_birth' => $customer['date_of_birth'] ?? null,
                'gender' => $customer['gender'] ?? null,
                'customer_segment' => $customer['customer_segment'] ?? null,
                'address' => $customer['address'] ?? null,
                'detailed_address' => $customer['detailed_address'] ?? null,
                'postal_code' => $customer['postal_code'] ?? null,
                'points' => $customer['points'] ?? 0,
                'loyalty_points' => $customer['loyalty_points'] ?? 0,
                'active_status' => $customer['active_status'] ?? true,
                'notes' => $customer['notes'] ?? null,
                'api_id' => $customer['api_id'] ?? ($source === 'api' ? ($customer['id'] ?? null) : null),
                'is_synced_to_api' => $customer['is_synced_to_api'] ?? false,
                'last_api_sync_at' => $customer['last_api_sync_at'] ?? null,
                '_source' => $source
            ];

            return $customerObj;
        }, $customers);
    }

    /**
     * Clear customer cache
     */
    public function clearCache(): void
    {
        Cache::forget('customers_api_cache');
        Log::info('CustomerService: Cache cleared');
    }

    /**
     * Test API connectivity
     */
    public function testApiConnection(): array
    {
        return $this->customerApiService->testConnection();
    }

    /**
     * Synchronize customers from API to local database
     */
    public function syncCustomersToDatabase(bool $forceRefresh = false): array
    {
        // Prevent concurrent syncs
        $lockKey = 'customer_sync_lock';
        if (Cache::has($lockKey)) {
            Log::warning('CustomerService: Sync already in progress, skipping');
            return [
                'success' => false,
                'error' => 'Sync already in progress. Please wait for the current sync to complete.',
                'stats' => [
                    'customers_created' => 0,
                    'customers_updated' => 0,
                ]
            ];
        }

        // Set sync lock (expires in 5 minutes)
        Cache::put($lockKey, true, 300);

        Log::info('CustomerService: Starting customer synchronization', [
            'force_refresh' => $forceRefresh,
            'lock_set' => true
        ]);

        try {
            // Get fresh data from API
            $apiResult = $this->customerApiService->fetchCustomers();

            if (!$apiResult['success']) {
                Log::error('CustomerService: API fetch failed during sync', [
                    'error' => $apiResult['error'] ?? 'Unknown error'
                ]);

                return [
                    'success' => false,
                    'error' => 'Failed to fetch data from API: ' . ($apiResult['error'] ?? 'Unknown error'),
                    'stats' => [
                        'customers_created' => 0,
                        'customers_updated' => 0,
                    ]
                ];
            }

            $customers = $apiResult['customers'];

            if (empty($customers)) {
                Log::info('CustomerService: No customers found in API response');
                return [
                    'success' => true,
                    'message' => 'No customers found in API',
                    'stats' => [
                        'customers_created' => 0,
                        'customers_updated' => 0,
                    ]
                ];
            }

            $result = DB::transaction(function () use ($customers) {
                return $this->performDatabaseSync($customers);
            });

            return $result;

        } catch (\Exception $e) {
            Log::error('CustomerService: Sync failed with exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'Sync failed: ' . $e->getMessage(),
                'stats' => [
                    'customers_created' => 0,
                    'customers_updated' => 0,
                ]
            ];
        } finally {
            // Always release the sync lock
            Cache::forget($lockKey);
            Log::info('CustomerService: Sync lock released');
        }
    }

    /**
     * Perform smart database sync with API data (update existing, create new)
     */
    private function performDatabaseSync(array $customers): array
    {
        Log::info('CustomerService: Starting smart database sync', [
            'customers_count' => count($customers)
        ]);

        $stats = [
            'customers_created' => 0,
            'customers_updated' => 0,
        ];

        // Keep system customers like 'Guest Customer' or 'Walk-in'
        $systemCustomers = ['Guest Customer', 'Walk-in', 'guest customer', 'walk-in'];

        // Step 1: Sync customers from API (update existing or create new)
        Log::info('CustomerService: Syncing customers from API');

        foreach ($customers as $customerData) {
            $customerName = $customerData['name'] ?? $customerData['nama'] ?? 'Unknown Customer';
            $customerApiId = $customerData['id'] ?? null;

            // Skip if this customer name matches system customers
            if (in_array(strtolower($customerName), array_map('strtolower', $systemCustomers))) {
                Log::debug('CustomerService: Skipping system customer', ['name' => $customerName]);
                continue;
            }

            // Try to find existing customer by API ID first
            $customer = null;
            if ($customerApiId) {
                $customer = Customer::where('api_id', $customerApiId)->first();
            }

            $customerData = [
                'name' => $customerName,
                'email' => $customerData['email'] ?? null,
                'phone' => $customerData['phone'] ?? $customerData['telepon'] ?? null,
                'address' => $customerData['address'] ?? $customerData['alamat'] ?? null,
                'api_id' => $customerApiId,
                'points' => $customerData['points'] ?? $customerData['loyalty_points'] ?? 0,
            ];

            if ($customer) {
                // Update existing customer
                $customer->update($customerData);
                $stats['customers_updated']++;
                Log::debug('CustomerService: Updated customer', [
                    'id' => $customer->id,
                    'name' => $customer->name,
                    'api_id' => $customerApiId
                ]);
            } else {
                // Create new customer
                $customer = Customer::create($customerData);
                $stats['customers_created']++;
                Log::debug('CustomerService: Created customer', [
                    'id' => $customer->id,
                    'name' => $customer->name,
                    'api_id' => $customerApiId
                ]);
            }
        }

        // Clear cache after successful sync
        $this->clearCache();

        Log::info('CustomerService: Smart sync completed successfully', $stats);

        return [
            'success' => true,
            'message' => 'Customers synchronized successfully (smart update)',
            'stats' => $stats,
            'processed_customers' => count($customers)
        ];
    }

    /**
     * Force sync and update cache timestamp
     */
    public function forceSyncFromApi(): array
    {
        $result = $this->syncCustomersToDatabase(true);

        if ($result['success']) {
            Cache::put('customer_last_sync', now()->format('Y-m-d H:i:s'), 86400);
        }

        return $result;
    }

    /**
     * Get sync status and statistics
     */
    public function getSyncStatus(): array
    {
        $apiResult = $this->customerApiService->testConnection();
        $localStats = $this->getLocalDatabaseStats();
        $dataSourceInfo = $this->getDataSourceInfo();

        return [
            'api_connected' => $apiResult['success'] ?? false,
            'api_status' => $apiResult['message'] ?? 'Unknown',
            'last_sync' => Cache::get('customer_last_sync', 'Never'),
            'data_source' => $dataSourceInfo['source'],
            'local_stats' => $localStats,
            'api_stats' => $dataSourceInfo,
            'sync_available' => $apiResult['success'] ?? false
        ];
    }

    /**
     * Get local database statistics
     */
    private function getLocalDatabaseStats(): array
    {
        return [
            'total_customers' => Customer::count(),
            'customers_with_api_id' => Customer::whereNotNull('api_id')->count(),
            'customers_without_api_id' => Customer::whereNull('api_id')->count(),
        ];
    }

    /**
     * Get data source information
     */
    private function getDataSourceInfo(): array
    {
        $customers = $this->getCustomers();
        $source = 'local';

        if (!empty($customers) && isset($customers[0]->_source)) {
            $source = $customers[0]->_source;
        }

        return [
            'source' => $source,
            'total_customers' => count($customers)
        ];
    }

    /**
     * Sync individual customer to database - Improved version
     */
    private function syncCustomer(array $customer): array
    {
        $customerData = [
            'name' => trim($customer['name'] ?? 'Unknown Customer'),
            'phone' => trim($customer['phone'] ?? ''),
            'email' => trim($customer['email'] ?? '') ?: null,
            'address' => trim($customer['address'] ?? '') ?: null,
            'points' => intval($customer['points'] ?? 0),
        ];

        $apiId = $customer['id'] ?? null;
        $existingCustomer = null;

        Log::debug('CustomerService: Syncing customer', [
            'name' => $customerData['name'],
            'api_id' => $apiId
        ]);

        // Strategy 1: Find by API ID (most reliable)
        if ($apiId) {
            $existingCustomer = Customer::where('api_id', $apiId)->first();
            if ($existingCustomer) {
                Log::debug('CustomerService: Found customer by API ID', ['id' => $existingCustomer->id]);
            }
        }

        // Strategy 2: Find by exact name and phone match
        if (!$existingCustomer && !empty($customerData['phone'])) {
            $existingCustomer = Customer::where('name', $customerData['name'])
                                      ->where('phone', $customerData['phone'])
                                      ->first();
            if ($existingCustomer) {
                Log::debug('CustomerService: Found customer by name and phone', ['id' => $existingCustomer->id]);
            }
        }

        // Strategy 3: Find by phone only (if unique)
        if (!$existingCustomer && !empty($customerData['phone']) && $customerData['phone'] !== '************') {
            $phoneMatches = Customer::where('phone', $customerData['phone'])->get();
            if ($phoneMatches->count() === 1) {
                $existingCustomer = $phoneMatches->first();
                Log::debug('CustomerService: Found customer by unique phone', ['id' => $existingCustomer->id]);
            }
        }

        if ($existingCustomer) {
            // Update existing customer with all data
            $updateData = $customerData;
            if ($apiId && !$existingCustomer->api_id) {
                $updateData['api_id'] = $apiId;
            }

            $existingCustomer->update($updateData);

            Log::info('CustomerService: Updated customer', [
                'id' => $existingCustomer->id,
                'name' => $customerData['name'],
                'api_id' => $apiId
            ]);

            return ['created' => 0, 'updated' => 1];
        } else {
            // Create new customer
            if ($apiId) {
                $customerData['api_id'] = $apiId;
            }

            $newCustomer = Customer::create($customerData);

            Log::info('CustomerService: Created new customer', [
                'id' => $newCustomer->id,
                'name' => $customerData['name'],
                'api_id' => $apiId
            ]);

            return ['created' => 1, 'updated' => 0];
        }
    }

    /**
     * Create customer locally and sync to API
     */
    public function createCustomer(array $customerData): array
    {
        try {
            DB::beginTransaction();

            // Create customer locally first
            $customer = Customer::create($customerData);

            // Try to sync to API automatically (network-dependent)
            $syncResult = $this->customerSyncService->syncCustomer($customer);

            if ($syncResult['success']) {
                Log::info('CustomerService: Customer created and synced to API', [
                    'id' => $customer->id,
                    'api_id' => $syncResult['api_id'] ?? null
                ]);
            } else {
                Log::warning('CustomerService: Customer created locally but API sync failed', [
                    'error' => $syncResult['error'] ?? 'Unknown',
                    'should_retry' => $syncResult['should_retry'] ?? false
                ]);
            }

            DB::commit();
            $this->clearCache();

            return [
                'success' => true,
                'customer' => $customer,
                'message' => 'Customer created successfully'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('CustomerService: Failed to create customer', ['error' => $e->getMessage()]);

            return [
                'success' => false,
                'error' => 'Failed to create customer: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update customer locally and sync to API
     */
    public function updateCustomer(Customer $customer, array $customerData): array
    {
        try {
            DB::beginTransaction();

            // Update customer locally first
            $customer->update($customerData);

            // Try to sync to API if authenticated and has API ID
            if (session()->has('api_token') && session('api_authenticated') && $customer->api_id) {
                $apiResult = $this->customerApiService->updateCustomer($customer->api_id, $customerData);

                if (!$apiResult['success']) {
                    Log::warning('CustomerService: Customer updated locally but API sync failed', ['error' => $apiResult['error'] ?? 'Unknown']);
                }
            }

            DB::commit();
            $this->clearCache();

            return [
                'success' => true,
                'customer' => $customer,
                'message' => 'Customer updated successfully'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('CustomerService: Failed to update customer', ['error' => $e->getMessage()]);

            return [
                'success' => false,
                'error' => 'Failed to update customer: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Delete customer locally and sync to API
     */
    public function deleteCustomer(Customer $customer): array
    {
        try {
            DB::beginTransaction();

            // Try to delete from API first if authenticated and has API ID
            if (session()->has('api_token') && session('api_authenticated') && $customer->api_id) {
                $apiResult = $this->customerApiService->deleteCustomer($customer->api_id);

                if (!$apiResult['success']) {
                    Log::warning('CustomerService: API delete failed, proceeding with local delete', ['error' => $apiResult['error'] ?? 'Unknown']);
                }
            }

            // Delete customer locally
            $customer->delete();

            DB::commit();
            $this->clearCache();

            return [
                'success' => true,
                'message' => 'Customer deleted successfully'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('CustomerService: Failed to delete customer', ['error' => $e->getMessage()]);

            return [
                'success' => false,
                'error' => 'Failed to delete customer: ' . $e->getMessage()
            ];
        }
    }

}
