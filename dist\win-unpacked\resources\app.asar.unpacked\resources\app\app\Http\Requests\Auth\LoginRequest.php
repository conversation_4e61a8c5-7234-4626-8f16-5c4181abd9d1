<?php

namespace App\Http\Requests\Auth;

use Illuminate\Auth\Events\Lockout;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
            'device_name' => ['nullable', 'string', 'max:255'],
        ];
    }

    /**
     * Attempt to authenticate the request's credentials against the API.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function authenticate(): void
    {
        $this->ensureIsNotRateLimited();

        try {
            Log::info('Attempting API authentication', [
                'email' => $this->email,
                'device_name' => $this->device_name ?? config('pos.default_device_name', 'POS System')
            ]);

            // Attempt API authentication
            $apiResponse = $this->authenticateWithApi();

            if ($apiResponse['success']) {
                Log::info('API authentication successful, processing login', [
                    'email' => $this->email,
                    'has_token' => !empty($apiResponse['token']),
                    'has_user' => !empty($apiResponse['user'])
                ]);

                // Store API token and user data in session - NO LOCAL USER CREATION
                $this->session()->put('api_token', $apiResponse['token']);
                $this->session()->put('api_user', $apiResponse['user']);
                $this->session()->put('api_authenticated', true);

                // Store outlet ID if available in the response
                $outletId = null;

                // Check for outlet in direct response
                if (isset($apiResponse['outlet']) && isset($apiResponse['outlet']['id'])) {
                    $outletId = $apiResponse['outlet']['id'];
                }
                // Check for outlets in user object
                elseif (isset($apiResponse['user']['outlets']) && !empty($apiResponse['user']['outlets'])) {
                    $firstOutlet = $apiResponse['user']['outlets'][0];
                    if (isset($firstOutlet['id'])) {
                        $outletId = $firstOutlet['id'];
                    }
                }

                if ($outletId) {
                    $this->session()->put('api_outlet_id', $outletId);

                    // Also cache outlet ID for background processes
                    \Cache::put('pos_outlet_id', $outletId, 86400); // Cache for 24 hours

                    // Also store outlet category for table feature determination
                    $outletCategory = null;
                    if (isset($apiResponse['outlet']['categories'])) {
                        $outletCategory = $apiResponse['outlet']['categories'];
                    } elseif (isset($apiResponse['user']['outlets']) && !empty($apiResponse['user']['outlets'])) {
                        $firstOutlet = $apiResponse['user']['outlets'][0];
                        $outletCategory = $firstOutlet['categories'] ?? null;
                    }

                    if ($outletCategory) {
                        $this->session()->put('api_outlet_category', $outletCategory);
                        \Cache::put('pos_outlet_category', $outletCategory, 86400); // Cache for 24 hours
                        $needsTables = $this->determineTableRequirement($outletCategory);
                        $this->session()->put('pos_needs_tables', $needsTables);
                    }

                    Log::info('Outlet information stored in session and cache', [
                        'outlet_id' => $outletId,
                        'outlet_category' => $outletCategory,
                        'needs_tables' => $needsTables ?? null,
                        'source' => isset($apiResponse['outlet']) ? 'direct' : 'user.outlets',
                        'cached_outlet_id' => \Cache::get('pos_outlet_id'),
                        'cached_outlet_category' => \Cache::get('pos_outlet_category')
                    ]);
                } else {
                    Log::warning('No outlet ID found in API response', [
                        'response_keys' => array_keys($apiResponse),
                        'user_outlets' => $apiResponse['user']['outlets'] ?? 'not_present'
                    ]);
                }

                Log::info('API authentication completed - user authenticated via API only');

                RateLimiter::clear($this->throttleKey());
                return;
            } else {
                Log::warning('API authentication returned unsuccessful response', [
                    'email' => $this->email,
                    'response' => $apiResponse
                ]);
            }
        } catch (\Exception $e) {
            Log::error('API Authentication exception', [
                'email' => $this->email,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            // Check if this is a user-friendly error message (rate limit, invalid credentials, etc.)
            $userFriendlyMessages = [
                'Too many login attempts',
                'Invalid email or password',
                'API response missing token'
            ];

            $isUserFriendlyError = false;
            foreach ($userFriendlyMessages as $friendlyMessage) {
                if (strpos($e->getMessage(), $friendlyMessage) !== false) {
                    $isUserFriendlyError = true;
                    break;
                }
            }

            if ($isUserFriendlyError) {
                // Show the user-friendly error message directly
                RateLimiter::hit($this->throttleKey());
                throw ValidationException::withMessages([
                    'email' => $e->getMessage(),
                ]);
            }
        }

        // If we reach here, API authentication failed
        RateLimiter::hit($this->throttleKey());

        throw ValidationException::withMessages([
            'email' => trans('auth.failed'),
        ]);
    }

    /**
     * Ensure the login request is not rate limited.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function ensureIsNotRateLimited(): void
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout($this));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'email' => trans('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the rate limiting throttle key for the request.
     */
    public function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->string('email')).'|'.$this->ip());
    }

    /**
     * Authenticate with the external API.
     *
     * @return array
     * @throws \Exception
     */
    private function authenticateWithApi(): array
    {
        $apiUrl = config('pos.api_base_url') . '/login';

        $response = Http::timeout(30)->post($apiUrl, [
            'email' => $this->email,
            'password' => $this->password,
            'device_name' => $this->device_name ?? config('pos.default_device_name', 'POS System'),
        ]);

        if (!$response->successful()) {
            // Handle specific HTTP status codes with user-friendly messages
            if ($response->status() === 429) {
                $retryAfter = $response->header('Retry-After');

                Log::warning('API authentication rate limited', [
                    'status' => $response->status(),
                    'retry_after' => $retryAfter,
                    'email' => $this->email
                ]);

                $message = 'Too many login attempts. Please try again';
                if ($retryAfter) {
                    $minutes = ceil($retryAfter / 60);
                    $message .= " in {$minutes} minute(s)";
                }
                $message .= '.';

                throw new \Exception($message);
            }

            if ($response->status() === 401) {
                Log::warning('API authentication failed - invalid credentials', [
                    'status' => $response->status(),
                    'email' => $this->email
                ]);

                throw new \Exception('Invalid email or password.');
            }

            // Log the error for debugging
            Log::error('API authentication HTTP error', [
                'status' => $response->status(),
                'body' => $response->body(),
                'url' => $apiUrl,
                'email' => $this->email
            ]);

            throw new \Exception('API authentication failed: HTTP ' . $response->status() . ' - ' . $response->body());
        }

        $data = $response->json();

        Log::info('API authentication response received', [
            'has_token' => isset($data['token']),
            'has_user' => isset($data['user']),
            'response_keys' => array_keys($data ?? [])
        ]);

        if (!isset($data['token'])) {
            Log::error('API response missing token', [
                'response_data' => $data,
                'url' => $apiUrl
            ]);
            throw new \Exception('API response missing token. Response: ' . json_encode($data));
        }

        return [
            'success' => true,
            'token' => $data['token'],
            'user' => $data['user'] ?? [
                'name' => $this->email,
                'email' => $this->email,
                'role' => 'Cashier'
            ],
            'outlet' => $data['outlet'] ?? null
        ];
    }

    /**
     * Determine if outlet requires table functionality based on category
     */
    private function determineTableRequirement(string $category): bool
    {
        $category = strtolower(trim($category));

        // FnB (Food & Beverage) outlets need tables
        if ($category === 'fnb' || str_contains($category, 'food') || str_contains($category, 'beverage')) {
            return true;
        }

        // VOO (Viera Oleh-Oleh) outlets don't need tables
        if ($category === 'voo' || str_contains($category, 'oleh-oleh') || str_contains($category, 'souvenir')) {
            return false;
        }

        // Default to requiring tables for unknown categories
        return true;
    }


}
