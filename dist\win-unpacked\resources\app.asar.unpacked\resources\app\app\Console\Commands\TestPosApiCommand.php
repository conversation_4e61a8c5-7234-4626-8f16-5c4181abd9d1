<?php

namespace App\Console\Commands;

use App\Services\PosApiService;
use Illuminate\Console\Command;

/**
 * Artisan command to test POS API connection and fetch products
 */
class TestPosApiCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'pos:test-api 
                            {--format=table : Output format (table, json, summary)}
                            {--cache : Use cached results if available}
                            {--connection-only : Only test connection, don\'t fetch products}';

    /**
     * The console command description.
     */
    protected $description = 'Test POS API connection and fetch products';

    private PosApiService $posApiService;

    public function __construct(PosApiService $posApiService)
    {
        parent::__construct();
        $this->posApiService = $posApiService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Testing POS API Connection...');
        $this->newLine();

        // Test connection first
        $connectionResult = $this->testConnection();
        
        if (!$connectionResult) {
            return Command::FAILURE;
        }

        // If only testing connection, stop here
        if ($this->option('connection-only')) {
            $this->info('✅ Connection test completed successfully!');
            return Command::SUCCESS;
        }

        // Fetch products
        $this->info('📦 Fetching products...');
        $result = $this->posApiService->fetchProducts();

        if (!$result['success']) {
            $this->error('❌ Failed to fetch products: ' . $result['error']);
            return Command::FAILURE;
        }

        $this->displayResults($result);
        return Command::SUCCESS;
    }

    /**
     * Test API connection
     */
    private function testConnection(): bool
    {
        $this->info('🔗 Testing connection...');
        
        $result = $this->posApiService->testConnection();
        
        if ($result['success']) {
            $this->info('✅ Connection successful!');
            $this->line("   Status Code: {$result['status_code']}");
            
            if (isset($result['response_time'])) {
                $responseTime = round($result['response_time'] * 1000, 2);
                $this->line("   Response Time: {$responseTime}ms");
            }
            
            $this->newLine();
            return true;
        }

        $this->error('❌ Connection failed: ' . $result['error']);
        return false;
    }

    /**
     * Display results based on format option
     */
    private function displayResults(array $result): void
    {
        $products = $result['data']['data'] ?? [];
        $format = $this->option('format');

        $this->info("✅ Successfully fetched " . count($products) . " products!");
        $this->newLine();

        switch ($format) {
            case 'json':
                $this->displayJsonFormat($result);
                break;
            case 'summary':
                $this->displaySummaryFormat($products);
                break;
            case 'table':
            default:
                $this->displayTableFormat($products);
                break;
        }
    }

    /**
     * Display results in table format
     */
    private function displayTableFormat(array $products): void
    {
        if (empty($products)) {
            $this->warn('No products found.');
            return;
        }

        $tableData = [];
        foreach (array_slice($products, 0, 10) as $product) { // Show first 10 products
            $tableData[] = [
                'ID' => $product['id'] ?? 'N/A',
                'Name' => substr($product['name'] ?? 'Unknown', 0, 30),
                'Price' => '$' . number_format($product['price'] ?? 0, 2),
                'Category' => $product['category']['name'] ?? 'N/A',
                'Status' => ($product['is_active'] ?? false) ? '✅ Active' : '❌ Inactive',
            ];
        }

        $this->table(
            ['ID', 'Name', 'Price', 'Category', 'Status'],
            $tableData
        );

        if (count($products) > 10) {
            $this->info('... and ' . (count($products) - 10) . ' more products');
        }
    }

    /**
     * Display results in JSON format
     */
    private function displayJsonFormat(array $result): void
    {
        $this->line(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
    }

    /**
     * Display results in summary format
     */
    private function displaySummaryFormat(array $products): void
    {
        $totalProducts = count($products);
        $activeProducts = count(array_filter($products, fn($p) => $p['is_active'] ?? false));
        $inactiveProducts = $totalProducts - $activeProducts;

        // Group by category
        $categories = [];
        foreach ($products as $product) {
            $categoryName = $product['category']['name'] ?? 'Uncategorized';
            $categories[$categoryName] = ($categories[$categoryName] ?? 0) + 1;
        }

        // Price statistics
        $prices = array_column($products, 'price');
        $prices = array_filter($prices, fn($p) => is_numeric($p));
        
        $this->info('📊 Product Summary:');
        $this->line("   Total Products: {$totalProducts}");
        $this->line("   Active Products: {$activeProducts}");
        $this->line("   Inactive Products: {$inactiveProducts}");
        $this->newLine();

        if (!empty($prices)) {
            $avgPrice = array_sum($prices) / count($prices);
            $minPrice = min($prices);
            $maxPrice = max($prices);
            
            $this->info('💰 Price Statistics:');
            $this->line('   Average Price: $' . number_format($avgPrice, 2));
            $this->line('   Min Price: $' . number_format($minPrice, 2));
            $this->line('   Max Price: $' . number_format($maxPrice, 2));
            $this->newLine();
        }

        if (!empty($categories)) {
            $this->info('📂 Categories:');
            foreach ($categories as $category => $count) {
                $this->line("   {$category}: {$count} products");
            }
        }
    }
}
