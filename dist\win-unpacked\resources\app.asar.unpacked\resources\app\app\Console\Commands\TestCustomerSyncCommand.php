<?php

namespace App\Console\Commands;

use App\Services\CustomerService;
use Illuminate\Console\Command;

class TestCustomerSyncCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:test-customer-sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test customer sync functionality to debug issues';

    /**
     * Execute the console command.
     */
    public function handle(CustomerService $customerService): int
    {
        $this->info('🔄 Testing Customer Sync Functionality');
        $this->newLine();

        // First test the API fetch directly
        $this->info('🌐 Testing Customer API Fetch...');
        $customerApiService = app(\App\Services\CustomerApiService::class);
        $apiResult = $customerApiService->fetchCustomers();

        $this->info('API Fetch Result:');
        $this->info('Success: ' . ($apiResult['success'] ? 'YES' : 'NO'));

        if ($apiResult['success']) {
            $this->info('Customers Count: ' . count($apiResult['customers']));
            if (!empty($apiResult['customers'])) {
                $sample = $apiResult['customers'][0];
                $this->info('Sample Customer from API:');
                $this->info('  - ID: ' . ($sample['id'] ?? 'N/A'));
                $this->info('  - Name: ' . ($sample['name'] ?? 'N/A'));
                $this->info('  - Phone: ' . ($sample['phone'] ?? 'N/A'));
                $this->info('  - Email: ' . ($sample['email'] ?? 'N/A'));
                $this->info('  - Points: ' . ($sample['points'] ?? 'N/A'));
            }
        } else {
            $this->error('API Error: ' . ($apiResult['error'] ?? 'Unknown'));
        }

        $this->newLine();

        // Test the customer sync process
        $this->info('👥 Starting Customer Sync...');
        $syncResult = $customerService->forceSyncFromApi();

        $this->newLine();
        $this->info('📊 Customer Sync Results:');
        $this->info('Success: ' . ($syncResult['success'] ? 'YES' : 'NO'));

        if (isset($syncResult['error'])) {
            $this->error('Error: ' . $syncResult['error']);
        }

        if (isset($syncResult['stats'])) {
            $stats = $syncResult['stats'];
            $this->info('Statistics:');
            $this->info('  Customers Created: ' . $stats['customers_created']);
            $this->info('  Customers Updated: ' . $stats['customers_updated']);
        }

        $this->newLine();

        // Check what's in the database now
        $this->info('📋 Database Status:');
        $customers = \App\Models\Customer::count();
        $customersWithApiId = \App\Models\Customer::whereNotNull('api_id')->count();

        $this->info("  Total Customers: {$customers}");
        $this->info("  Customers with API ID: {$customersWithApiId}");

        // Show some sample data
        $this->newLine();
        $this->info('📝 Sample Customer Data:');
        $sampleCustomers = \App\Models\Customer::limit(5)->get();
        foreach ($sampleCustomers as $customer) {
            $apiId = $customer->api_id ? $customer->api_id : 'N/A';
            $this->info("  - {$customer->name} (Phone: {$customer->phone}, Email: {$customer->email}, API ID: {$apiId})");
        }

        return 0;
    }
}
