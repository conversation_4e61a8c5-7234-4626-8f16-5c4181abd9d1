<?php

namespace App\Guards;

use Illuminate\Contracts\Auth\Authenticatable;

/**
 * Simple API User class that implements Authenticatable
 */
class ApiUser implements Authenticatable
{
    protected $attributes;

    public function __construct(array $attributes)
    {
        $this->attributes = $attributes;
    }

    public function getAuthIdentifierName(): string
    {
        return 'id';
    }

    public function getAuthIdentifier()
    {
        return $this->attributes['id'] ?? $this->attributes['email'];
    }

    public function getAuthPasswordName(): string
    {
        return 'password';
    }

    public function getAuthPassword(): string
    {
        return '';
    }

    public function getRememberToken(): ?string
    {
        return null;
    }

    public function setRememberToken($value): void
    {
        // Not implemented for API users
    }

    public function getRememberTokenName(): string
    {
        return '';
    }

    public function __get($key)
    {
        return $this->attributes[$key] ?? null;
    }

    public function __isset($key): bool
    {
        return isset($this->attributes[$key]);
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return strtolower($this->attributes['role'] ?? '') === 'admin';
    }

    /**
     * Check if user has a specific role
     */
    public function hasRole(string $role): bool
    {
        return strtolower($this->attributes['role'] ?? '') === strtolower($role);
    }

    /**
     * Get user's role
     */
    public function getRole(): string
    {
        return $this->attributes['role'] ?? 'Cashier';
    }

    /**
     * Get user's name
     */
    public function getName(): string
    {
        return $this->attributes['name'] ?? $this->attributes['email'] ?? 'Unknown';
    }

    /**
     * Get user's email
     */
    public function getEmail(): string
    {
        return $this->attributes['email'] ?? '';
    }

    /**
     * Get all attributes
     */
    public function getAttributes(): array
    {
        return $this->attributes;
    }

    /**
     * Set an attribute
     */
    public function setAttribute(string $key, $value): void
    {
        $this->attributes[$key] = $value;
    }

    /**
     * Check if user has a specific permission
     */
    public function hasPermission(string $permission): bool
    {
        // For now, admin has all permissions
        return $this->isAdmin();
    }

    /**
     * Check if user can access admin features (admin, keptok, karyawan roles with proper jabatan).
     */
    public function canAccessAdminFeatures(): bool
    {
        $role = strtolower($this->attributes['role'] ?? '');

        // Admin role bypasses jabatan check
        if ($role === 'admin') {
            return true;
        }

        // For keptok and karyawan roles, check jabatan
        if (in_array($role, ['keptok', 'karyawan'])) {
            $jabatan = $this->getJabatan();
            return in_array($jabatan, ['kepala toko', 'kasir']);
        }

        return false;
    }

    /**
     * Get the jabatan (position) of the user.
     * Handles nested jabatan field in karyawan object.
     */
    public function getJabatan(): string
    {
        // First check if jabatan is directly in attributes
        if (isset($this->attributes['jabatan'])) {
            return strtolower($this->attributes['jabatan']);
        }

        // Check if jabatan is nested in karyawan object
        if (isset($this->attributes['karyawan']['jabatan'])) {
            return strtolower($this->attributes['karyawan']['jabatan']);
        }

        return '';
    }

    /**
     * Check if user can access menu management features (Categories and Menu Items).
     * Karyawan role is excluded from menu management regardless of jabatan.
     */
    public function canAccessMenuManagement(): bool
    {
        $role = strtolower($this->attributes['role'] ?? '');

        // Admin role can access everything
        if ($role === 'admin'|| $role === 'keptok') {
            return true;
        }

        // Keptok role can access menu management
        if ($role === 'keptok') {
            $jabatan = $this->getJabatan();
            return in_array($jabatan, ['kepala toko', 'kasir']);
        }

        // Karyawan role cannot access menu management
        if ($role === 'karyawan') {
            return false;
        }

        return false;
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return $this->attributes;
    }
}
