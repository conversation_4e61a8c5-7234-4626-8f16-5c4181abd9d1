<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestApiResponse extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:test-api-response {--email=} {--password=} {--device-name=} {--location-id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test what the API actually returns';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->option('email') ?: $this->ask('Enter API email');
        $password = $this->option('password') ?: $this->secret('Enter API password');
        $deviceName = $this->option('device-name') ?: $this->ask('Enter device name', 'Oppo');
        $locationId = $this->option('location-id') ?: $this->ask('Enter location ID (optional)', '');

        if (!$email || !$password) {
            $this->error('Email and password are required');
            return Command::FAILURE;
        }

        $baseUrl = 'http://viera-filament.test/api/pos';
        
        $this->info('🔐 Testing Authentication...');
        
        try {
            // Test authentication
            $loginData = [
                'email' => $email,
                'password' => $password,
                'device_name' => $deviceName,
            ];

            if (!empty($locationId)) {
                $loginData['location_id'] = $locationId;
            }

            $authResponse = Http::timeout(30)
                ->post($baseUrl . '/login', $loginData);

            if (!$authResponse->successful()) {
                $this->error('❌ Authentication failed');
                $this->error('Status: ' . $authResponse->status());
                $this->error('Response: ' . $authResponse->body());
                return Command::FAILURE;
            }

            $authData = $authResponse->json();
            $token = $authData['token'] ?? $authData['access_token'] ?? null;

            if (!$token) {
                $this->error('❌ No token in authentication response');
                $this->line('Response: ' . json_encode($authData, JSON_PRETTY_PRINT));
                return Command::FAILURE;
            }

            $this->info('✅ Authentication successful');
            $this->info('Token length: ' . strlen($token));
            
            $this->newLine();
            $this->info('📦 Testing Product Fetch...');

            // Test product fetch
            $productsResponse = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json',
            ])
            ->timeout(30)
            ->get($baseUrl . '/sync/products');

            if (!$productsResponse->successful()) {
                $this->error('❌ Product fetch failed');
                $this->error('Status: ' . $productsResponse->status());
                $this->error('Response: ' . $productsResponse->body());
                return Command::FAILURE;
            }

            $productsData = $productsResponse->json();
            $this->info('✅ Product fetch successful');
            
            $this->newLine();
            $this->info('📋 API Response Structure:');
            $this->line(json_encode($productsData, JSON_PRETTY_PRINT));
            
            $this->newLine();
            $this->info('📊 Summary:');
            
            if (isset($productsData['data']) && is_array($productsData['data'])) {
                $products = $productsData['data'];
                $this->info('Total products: ' . count($products));
                
                if (count($products) > 0) {
                    $this->info('First product structure:');
                    $this->line(json_encode($products[0], JSON_PRETTY_PRINT));
                    
                    // Group by categories
                    $categories = [];
                    foreach ($products as $product) {
                        $categoryName = $product['category']['name'] ?? $product['category'] ?? 'Uncategorized';
                        if (!isset($categories[$categoryName])) {
                            $categories[$categoryName] = 0;
                        }
                        $categories[$categoryName]++;
                    }
                    
                    $this->newLine();
                    $this->info('Categories found:');
                    foreach ($categories as $category => $count) {
                        $this->line("  - {$category}: {$count} items");
                    }
                }
            } else {
                $this->warn('Unexpected response structure - no data array found');
            }

        } catch (\Exception $e) {
            $this->error('❌ Error: ' . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
